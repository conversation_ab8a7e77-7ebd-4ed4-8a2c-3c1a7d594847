<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/send_spi_layout"
        android:layout_width="wrap_content"
        android:layout_height="30dp">
        <CheckBox
            android:id="@+id/checkBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="spi0" />
        <CheckBox
            android:id="@+id/checkBox1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="spi1" />

        <CheckBox
            android:id="@+id/checkBox2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="spi2" />

        <CheckBox
            android:id="@+id/checkBox3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="spi3" />

        <CheckBox
            android:id="@+id/checkBox4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="spi4" />
        <CheckBox
            android:id="@+id/checkBox5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="spi5" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.63"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/send_interval_layout"
            android:layout_width="wrap_content"
            android:layout_height="40dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="发送时间间隔："/>
            <EditText
                android:id="@+id/edit_send"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="3"
                android:inputType="number"
                android:maxLength="2"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ms"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="  子机心跳间隔："/>
            <EditText
                android:id="@+id/edit_subsend"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:inputType="number"
                android:maxLength="2"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="s"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/send_button_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            >

            <Button
                android:id="@+id/btn_send"
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="发送"/>

            <Button
                android:id="@+id/btn_pair"
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="配对"/>
            <Button
                android:id="@+id/btn_cancelpair"
                android:layout_width="90dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="退出配对"/>

            <Button
                android:id="@+id/btn_version"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="查版本"/>
            <Button
                android:id="@+id/btn_upgrade"
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="升级"/>
            <Button
                android:id="@+id/btn_dut"
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="DUT"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/send_subbutton_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            >
            <Button
                android:id="@+id/btn_sendtext"
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="字幕"/>
            <Button
                android:id="@+id/btn_subversion"
                android:layout_width="90dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="子设id"/>
            <Button
                android:id="@+id/btn_subupgrade"
                android:layout_width="90dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="子进升级"/>
            <Button
                android:id="@+id/btn_subdut"
                android:layout_width="90dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="子设角色"/>
            <Button
                android:id="@+id/btn_receive"
                android:layout_width="90dp"
                android:layout_height="40dp"
                android:textSize="12sp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:text="录音"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/send_ttbutton_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            >
            <Button
                android:id="@+id/btn_saystop"
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="抢麦"/>

            <Button
                android:id="@+id/btn_upbag"
                android:layout_width="110dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="不上报心跳"/>

            <Button
                android:id="@+id/btn_firepair"
                android:layout_width="130dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="全部解除配对"/>
            <Button
                android:id="@+id/btn_clearLog"
                android:layout_width="130dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:textSize="12sp"
                android:text="清TL统计"/>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:layout_marginTop="8dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/send_receiv0_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:orientation="horizontal"
            >
            <TextView
                android:id="@+id/text_receive0"
                android:layout_width="202dp"
                android:layout_height="60dp"
                android:textSize="10sp"
                android:scrollbars="vertical"
                android:fadeScrollbars="false"/>

            <TextView
                android:id="@+id/text_receive3"
                android:layout_width="202dp"
                android:layout_height="60dp"
                android:textSize="10sp"
                android:scrollbars="vertical"
                android:fadeScrollbars="false"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/send_receiv1_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:orientation="horizontal"
            >
            <TextView
                android:id="@+id/text_receive1"
                android:layout_width="202dp"
                android:layout_height="60dp"
                android:textSize="10sp"
                android:scrollbars="vertical"
                android:fadeScrollbars="false"/>

            <TextView
                android:id="@+id/text_receive4"
                android:layout_width="202dp"
                android:layout_height="60dp"
                android:textSize="10sp"
                android:scrollbars="vertical"
                android:fadeScrollbars="false"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/send_receiv2_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:orientation="horizontal"
            >
            <TextView
                android:id="@+id/text_receive2"
                android:layout_width="202dp"
                android:layout_height="60dp"
                android:textSize="10sp"
                android:scrollbars="vertical"
                android:fadeScrollbars="false"/>

            <TextView
                android:id="@+id/text_receive5"
                android:layout_width="202dp"
                android:layout_height="60dp"
                android:textSize="10sp"
                android:scrollbars="vertical"
                android:fadeScrollbars="false"/>
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:scrollbars="vertical"
            android:layout_width="match_parent"
            android:layout_height="300dp"/>
    </LinearLayout>

</LinearLayout>
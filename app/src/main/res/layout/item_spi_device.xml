<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:orientation="horizontal"
    android:padding="8dp"
    android:gravity="center_vertical"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/upsoundtotal"
            android:text="rf上行"
            android:textSize="8sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/downspitotal"
            android:text="spi下行"
            android:textSize="8sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/upspitotal"
            android:text="spi上行"
            android:textSize="8sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>

    <LinearLayout
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/downindextotal"
            android:text="指令下行"
            android:textSize="8sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/upindextotal"
            android:text="指令上行"
            android:textSize="8sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/rf_rssi"
            android:text="远端rssi"
            android:textSize="8sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</LinearLayout>

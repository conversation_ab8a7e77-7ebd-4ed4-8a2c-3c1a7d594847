package com.neviewtech.subg;

import android.util.Log;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public abstract class ScheduledTask {
    private static final String TAG = ScheduledTask.class.getSimpleName();

    private final ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
    private volatile ScheduledFuture<?> task;
    private final String name;
    private int delayMilliSeconds;

    public ScheduledTask(String name, int delayMilliSeconds) {
        this.name = name;
        this.delayMilliSeconds = delayMilliSeconds;
    }

    public void start() {
        if (task != null) {
            Log.i(TAG,name + " Already started");
            return;
        }

        task = executor.scheduleWithFixedDelay(this::execute, 0, getDelayMilliSeconds(), TimeUnit.MILLISECONDS);
    }

    public void stop() {
        if(task != null) {
            task.cancel(true);
            task = null;
            Log.i(TAG,name + " stop");
        }
    }

    public void destroy() {
        task.cancel(true);
        executor.shutdown();
        task = null;
        Log.i(TAG,name + " destroy");
    }

    public int getDelayMilliSeconds() {
        return delayMilliSeconds;
    }

    public void setDelayMilliSeconds(int delayMilliSeconds) {
        this.delayMilliSeconds = delayMilliSeconds;
    }

    public abstract void execute();
}

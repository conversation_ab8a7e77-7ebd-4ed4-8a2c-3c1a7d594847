package com.neviewtech.subg;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import android.annotation.SuppressLint;
import android.media.AudioAttributes;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.AudioTrack;
import android.media.MediaRecorder;
import android.os.Bundle;
import android.os.Environment;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.CheckBox;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;

import com.neviewtech.subg.ByteArrayStorage;

public class MainActivity extends AppCompatActivity implements View.OnClickListener {

    private static final String TAG = SubG.TAG;
    private SpiDeviceAdapter adapter;
    private List<SpiDeviceModel> deviceList = new ArrayList<>();
    CheckBox checkBox, checkBox1, checkBox2, checkBox3, checkBox4, checkBox5, checkBox6, checkBox7, checkBox8, checkBox9, checkBox10, checkBox11;
    private SubG mSubG, mSubG1, mSubG2, mSubG3, mSubG4, mSubG5;
    private Button mBtnSend, mBtnReceive, mBtnPair, mBtncancelPair, mBtnsendText;
    private Button mBtnVersion, mBtnUpgrade, mBtnDut, mBtnsubVersion, mBtnsubUpgrade, mBtnsubDut;
    private Button mBtnsaystop, mBtnallowsay, mBtnupbag, mBtnstopbag, mBtnfirepair, mBtnclearlog;
    private TextView mTextSend, mTextReceive, mTextloss;
    private TextView mTextReceive0, mTextReceive1, mTextReceive2;
    private TextView mTextReceive3, mTextReceive4, mTextReceive5;
    private EditText mEditSend, mEditSpi, mEditSubheart;
    private RadioGroup mRGSend;
    private boolean mTypeSubGOrTLink, mIsSend, mIsReceive, mSendMicOrFile;
    private boolean mTstopsay = false;
    private boolean mTupbag = true;
    private boolean mTfirepair = false;
    private ByteArrayStorage gstorage = new ByteArrayStorage();
    private ByteArrayStorage gstorage1 = new ByteArrayStorage();

    private boolean mFirstSend, mFirstReceive, mFirstSend1, mFirstReceive1, mFirstSend2, mFirstReceive2, mFirstSend3, mFirstReceive3, mFirstSend4, mFirstReceive4, mFirstSend5, mFirstReceive5;
    private int mallowMic0 = 0;
    private int mallowMic1 = 0;
    private int mallowMic2 = 0;
    private int mallowMic3 = 0;
    private int mallowMic4 = 0;
    private int mallowMic5 = 0;
    private boolean[] mStartPair = new boolean[6];
    private boolean[] mCancelPair = new boolean[6];
    private boolean[] mSendText = new boolean[6];
    private boolean[] mVersion = new boolean[6];
    private boolean[] mUpgrade = new boolean[6];
    private boolean[] mDut = new boolean[6];
    private boolean[] msubVersion = new boolean[6];
    private boolean[] msubUpgrade = new boolean[6];
    private boolean[] msubDut = new boolean[6];
    private boolean[] mTLclearlog = new boolean[6];
    private int pairNumbers = 0;
    private int mTLinkIndex = 1;
    private int mTLinkIndex1 = 1;
    private int mTLinkIndex2 = 1;
    private int mTLinkIndex3 = 1;
    private int mTLinkIndex4 = 1;
    private int mTLinkIndex5 = 1;

    private int mTLinkspiuptotal = 0;
    private int mTLinkspiuplastIndex = 0;
    private int mTLinkspiuplosstotal = 0;
    private int mTLinkspidownlosstotal = 0;
    private int[] zhilingindex = new int[9];
    private int mTLinkindexdownlosstotal = 0;

    private int mTLinkspiuptotal1 = 0;
    private int mTLinkspiuplastIndex1 = 0;
    private int mTLinkspiuplosstotal1 = 0;
    private int mTLinkspidownlosstotal1 = 0;
    private int[] zhilingindex1 = new int[9];
    private int mTLinkindexdownlosstotal1 = 0;

    private int mTLinkspiuptotal2 = 0;
    private int mTLinkspiuplastIndex2 = 0;
    private int mTLinkspiuplosstotal2 = 0;
    private int mTLinkspidownlosstotal2 = 0;
    private int[] zhilingindex2 = new int[9];
    private int mTLinkindexdownlosstotal2 = 0;

    private int mTLinkspiuptotal3 = 0;
    private int mTLinkspiuplastIndex3 = 0;
    private int mTLinkspiuplosstotal3 = 0;
    private int mTLinkspidownlosstotal3 = 0;
    private int[] zhilingindex3 = new int[9];
    private int mTLinkindexdownlosstotal3 = 0;

    private int mTLinkspiuptotal4 = 0;
    private int mTLinkspiuplastIndex4 = 0;
    private int mTLinkspiuplosstotal4 = 0;
    private int mTLinkspidownlosstotal4 = 0;
    private int[] zhilingindex4 = new int[9];
    private int mTLinkindexdownlosstotal4 = 0;

    private int mTLinkspiuptotal5 = 0;
    private int mTLinkspiuplastIndex5 = 0;
    private int mTLinkspiuplosstotal5 = 0;
    private int mTLinkspidownlosstotal5 = 0;
    private int[] zhilingindex5 = new int[9];
    private int mTLinkindexdownlosstotal5 = 0;

    private int mTLinkRFIndex = 0;
    private int mTLinkRFlosstotal = 0;
    private int localrfrssi = 0;
    private int romoterfrssi = 0;

    private int mTLinkRFIndex1 = 0;
    private int mTLinkRFlosstotal1 = 0;
    private int localrfrssi1 = 0;
    private int romoterfrssi1 = 0;

    private int mTLinkRFIndex2 = 0;
    private int mTLinkRFlosstotal2 = 0;
    private int localrfrssi2 = 0;
    private int romoterfrssi2 = 0;

    private int mTLinkRFIndex3 = 0;
    private int mTLinkRFlosstotal3 = 0;
    private int localrfrssi3 = 0;
    private int romoterfrssi3 = 0;

    private int mTLinkRFIndex4 = 0;
    private int mTLinkRFlosstotal4 = 0;
    private int localrfrssi4 = 0;
    private int romoterfrssi4 = 0;

    private int mTLinkRFIndex5 = 0;
    private int mTLinkRFlosstotal5 = 0;
    private int localrfrssi5 = 0;
    private int romoterfrssi5 = 0;

    private int[] mTLinkindexuptotal = new int[3];
    private int[] mTLinkindexuplastIndex = new int[3];
    private int mTLinkindexuplosstotal = 0;
    private int[] mTLinkindexuptotal1 = new int[3];
    private int[] mTLinkindexuplastIndex1 = new int[3];
    private int mTLinkindexuplosstotal1 = 0;
    private int[] mTLinkindexuptotal2 = new int[3];
    private int[] mTLinkindexuplastIndex2 = new int[3];
    private int mTLinkindexuplosstotal2 = 0;
    private int[] mTLinkindexuptotal3 = new int[3];
    private int[] mTLinkindexuplastIndex3 = new int[3];
    private int mTLinkindexuplosstotal3 = 0;
    private int[] mTLinkindexuptotal4 = new int[3];
    private int[] mTLinkindexuplastIndex4 = new int[3];
    private int mTLinkindexuplosstotal4 = 0;
    private int[] mTLinkindexuptotal5 = new int[3];
    private int[] mTLinkindexuplastIndex5 = new int[3];
    private int mTLinkindexuplosstotal5 = 0;
    public byte[] nowsubMac = new byte[6];
    public byte[] nowsubMac1 = new byte[6];
    public byte[] nowsubMac2 = new byte[6];
    public byte[] nowsubMac3 = new byte[6];
    public byte[] nowsubMac4 = new byte[6];
    public byte[] nowsubMac5 = new byte[6];

    private int mTReceivermicbegin = 0;
    private int whichmicbegin = -1;
    private ScheduledTask mSendTask, mReceiveTask, mSendTask1, mSendTask2, mSendTask3, mSendTask4, mSendTask5;

    private static final String PCM_SEND_FILE_PATH = Environment.getExternalStorageDirectory() + File.separator + "spi.pcm";
    private static final String PCM_RECEIVE_FILE_PATH = Environment.getExternalStorageDirectory() + File.separator + "spi_receive.pcm";
    private static final String PCM_RECEIVE_FILE_PATH1 = Environment.getExternalStorageDirectory() + File.separator + "spi_receive1.pcm";
    private static final String PCM_RECEIVE_FILE_PATH2 = Environment.getExternalStorageDirectory() + File.separator + "spi_receive2.pcm";
    private static final String PCM_RECEIVE_FILE_PATH3 = Environment.getExternalStorageDirectory() + File.separator + "spi_receive3.pcm";
    private static final String PCM_RECEIVE_FILE_PATH4 = Environment.getExternalStorageDirectory() + File.separator + "spi_receive4.pcm";
    private static final String PCM_RECEIVE_FILE_PATH5 = Environment.getExternalStorageDirectory() + File.separator + "spi_receive5.pcm";
    private FileInputStream mFIS;
    private FileOutputStream mFOS;
    private FileInputStream mFIS1;
    private FileOutputStream mFOS1;
    private FileInputStream mFIS2;
    private FileOutputStream mFOS2;
    private FileInputStream mFIS3;
    private FileOutputStream mFOS3;
    private FileInputStream mFIS4;
    private FileOutputStream mFOS4;
    private FileInputStream mFIS5;
    private FileOutputStream mFOS5;
    private static final int OPUS_LEN = 40;
    private byte[] mAudioData, mReceiveData, mFrameData;
    private byte[] mAudioData1, mReceiveData1, mFrameData1;
    private byte[] mAudioData2, mReceiveData2, mFrameData2;
    private byte[] mAudioData3, mReceiveData3, mFrameData3;
    private byte[] mAudioData4, mReceiveData4, mFrameData4;
    private byte[] mAudioData5, mReceiveData5, mFrameData5;
    private String tempstringoff;
    private String tempstringoff1;
    private String tempstringoff2;
    private String tempstringoff3;
    private String tempstringoff4;
    private String tempstringoff5;
    private AudioTrack mStreamAudioTrack, mStreamAudioTrack1, mStreamAudioTrack2, mStreamAudioTrack3, mStreamAudioTrack4, mStreamAudioTrack5;
    private AudioRecord mAudioRecord;
    private int lasttlinkIndex = 0;
    private int totallinkIndex = 0;
    private int resetfileopen = 0;
    private int resetfileopen1 = 0;
    private int resetfileopen2 = 0;
    private int resetfileopen3 = 0;
    private int resetfileopen4 = 0;
    private int resetfileopen5 = 0;
    private static final int SPI_EACH_LEN = 1792;
    private static final int SPI_EACH_AUDIO_LEN = 720;

    private int firsthighstatus = 0;
    private int firstlowstatus = 0;
    private int secondhighstatus = 0;
    private int secondlowstatus = 0;
    private int thirthhighstatus = 0;
    private int thirthlowstatus = 0;
    private int[] fourthstatus = new int[6];
    private int fivethstatus = 0;

    int totalsendnumber = 0;
    int removefirstnumber = 0;
    int losstotalnumber = 0;
    private static final String LOG_FILE_PATH = Environment.getExternalStorageDirectory().getAbsolutePath() + "/SpiLog/";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        RecyclerView recyclerView = findViewById(R.id.recyclerView);
        adapter = new SpiDeviceAdapter(deviceList, new SpiDeviceAdapter.OnItemClickListener() {
            @Override
            public void onConnectClicked(SpiDeviceModel device, boolean isconnect) {
                //connectToDevice(device,isconnect);
            }

            @Override
            public void onPairClicked(SpiDeviceModel device, boolean isPair) {

            }
        });
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
        SpiLog.deleteFilesInDirectory(LOG_FILE_PATH);
        deviceList.add(new SpiDeviceModel("spi0", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0));
        deviceList.add(new SpiDeviceModel("spi1", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0));
        deviceList.add(new SpiDeviceModel("spi2", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0));
        deviceList.add(new SpiDeviceModel("spi3", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0));
        deviceList.add(new SpiDeviceModel("spi4", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0));
        deviceList.add(new SpiDeviceModel("spi5", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0));

        Arrays.fill(mStartPair, false);
        Arrays.fill(mCancelPair, false);
        Arrays.fill(mSendText, false);
        Arrays.fill(mVersion, false);
        Arrays.fill(mUpgrade, false);
        Arrays.fill(mDut, false);
        Arrays.fill(msubVersion, false);
        Arrays.fill(msubUpgrade, false);
        Arrays.fill(msubDut, false);
        Arrays.fill(mTLclearlog, false);
        Arrays.fill(fourthstatus, 0);


        mBtnSend = findViewById(R.id.btn_send);
        mBtnReceive = findViewById(R.id.btn_receive);
        mTextReceive0 = findViewById(R.id.text_receive0);
        mTextReceive1 = findViewById(R.id.text_receive1);
        mTextReceive2 = findViewById(R.id.text_receive2);
        mTextReceive3 = findViewById(R.id.text_receive3);
        mTextReceive4 = findViewById(R.id.text_receive4);
        mTextReceive5 = findViewById(R.id.text_receive5);
        mEditSend = findViewById(R.id.edit_send);
        mEditSubheart = findViewById(R.id.edit_subsend);
        checkBox = findViewById(R.id.checkBox);
        checkBox1 = findViewById(R.id.checkBox1);
        checkBox2 = findViewById(R.id.checkBox2);
        checkBox3 = findViewById(R.id.checkBox3);
        checkBox4 = findViewById(R.id.checkBox4);
        checkBox5 = findViewById(R.id.checkBox5);

        mBtnPair = findViewById(R.id.btn_pair);
        mBtncancelPair = findViewById(R.id.btn_cancelpair);
        mBtnsendText = findViewById(R.id.btn_sendtext);
        mBtnVersion = findViewById(R.id.btn_version);
        mBtnUpgrade = findViewById(R.id.btn_upgrade);
        mBtnDut = findViewById(R.id.btn_dut);
        mBtnsubVersion = findViewById(R.id.btn_subversion);
        mBtnsubUpgrade = findViewById(R.id.btn_subupgrade);
        mBtnsubDut = findViewById(R.id.btn_subdut);


        mBtnsaystop = findViewById(R.id.btn_saystop);
        mBtnupbag = findViewById(R.id.btn_upbag);
        mBtnfirepair = findViewById(R.id.btn_firepair);
        mBtnclearlog = findViewById(R.id.btn_clearLog);

        mBtnPair.setOnClickListener(this);
        mBtncancelPair.setOnClickListener(this);
        mBtnsendText.setOnClickListener(this);
        mBtnVersion.setOnClickListener(this);
        mBtnUpgrade.setOnClickListener(this);
        mBtnDut.setOnClickListener(this);
        mBtnsubVersion.setOnClickListener(this);
        mBtnsubUpgrade.setOnClickListener(this);
        mBtnsubDut.setOnClickListener(this);

        mBtnsaystop.setOnClickListener(this);
        mBtnupbag.setOnClickListener(this);
        mBtnfirepair.setOnClickListener(this);
        mBtnclearlog.setOnClickListener(this);

        mSendMicOrFile = false;

        mAudioData = new byte[SPI_EACH_AUDIO_LEN];//5ms
        mReceiveData = new byte[SPI_EACH_LEN];
        mFrameData = new byte[SPI_EACH_LEN];
        mAudioData1 = new byte[SPI_EACH_AUDIO_LEN];//5ms
        mReceiveData1 = new byte[SPI_EACH_LEN];
        mFrameData1 = new byte[SPI_EACH_LEN];

        mAudioData2 = new byte[SPI_EACH_AUDIO_LEN];//5ms
        mReceiveData2 = new byte[SPI_EACH_LEN];
        mFrameData2 = new byte[SPI_EACH_LEN];

        mAudioData3 = new byte[SPI_EACH_AUDIO_LEN];//5ms
        mReceiveData3 = new byte[SPI_EACH_LEN];
        mFrameData3 = new byte[SPI_EACH_LEN];

        mAudioData4 = new byte[SPI_EACH_AUDIO_LEN];//5ms
        mReceiveData4 = new byte[SPI_EACH_LEN];
        mFrameData4 = new byte[SPI_EACH_LEN];

        mAudioData5 = new byte[SPI_EACH_AUDIO_LEN];//5ms
        mReceiveData5 = new byte[SPI_EACH_LEN];
        mFrameData5 = new byte[SPI_EACH_LEN];
        mEditSend.setText("2");
        mBtnSend.setOnClickListener(this);
        mBtnReceive.setOnClickListener(this);

        mTextReceive0.setMovementMethod(ScrollingMovementMethod.getInstance());
        mTextReceive1.setMovementMethod(ScrollingMovementMethod.getInstance());
        mTextReceive2.setMovementMethod(ScrollingMovementMethod.getInstance());
        mTextReceive3.setMovementMethod(ScrollingMovementMethod.getInstance());
        mTextReceive4.setMovementMethod(ScrollingMovementMethod.getInstance());
        mTextReceive5.setMovementMethod(ScrollingMovementMethod.getInstance());

        mSendTask = new ScheduledTask("send", 5) {
            @Override
            public void execute() {
                try {
                    //Log.e(TAG,"teric2");
                    Log.i(TAG, "mSendTask");
                    sendData();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };

        mSendTask1 = new ScheduledTask("send1", 5) {
            @Override
            public void execute() {
                try {
                    //Log.e(TAG,"teric2");
                    Log.i(TAG, "mSendTask1");
                    sendData1();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        mSendTask2 = new ScheduledTask("send2", 5) {
            @Override
            public void execute() {
                try {
                    //Log.e(TAG,"teric2");
                    Log.i(TAG, "mSendTask2");
                    sendData2();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        mSendTask3 = new ScheduledTask("send3", 5) {
            @Override
            public void execute() {
                try {
                    //Log.e(TAG,"teric2");
                    Log.i(TAG, "mSendTask3");
                    sendData3();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        mSendTask4 = new ScheduledTask("send4", 5) {
            @Override
            public void execute() {
                try {
                    //Log.e(TAG,"teric2");
                    Log.i(TAG, "mSendTask4");
                    sendData4();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        mSendTask5 = new ScheduledTask("send5", 5) {
            @Override
            public void execute() {
                try {
                    //Log.e(TAG,"teric2");
                    Log.i(TAG, "mSendTask5");
                    sendData5();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        initStreamAudioTrack();

        XXPermissions.with(this)
                //.permission(Permission.Group.STORAGE)
                .permission(Permission.MANAGE_EXTERNAL_STORAGE)
                .permission(Permission.RECORD_AUDIO)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (!allGranted) {
                            Log.e(TAG, "获取部分权限成功，但部分权限未正常授予");
                            Toast.makeText(getApplicationContext(), "file permission is lost", Toast.LENGTH_SHORT).show();
                            return;
                        }
                        initAudioRecorder();
                        openSendFile();
                        //openReceiveFile();
                        Log.e(TAG, "获取权限成功");
                    }

                    @Override
                    public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                        if (doNotAskAgain) {
                            Log.e(TAG, "被永久拒绝授权，请手动授予权限");
                            // 如果是被永久拒绝就跳转到应用权限系统设置页面
                            XXPermissions.startPermissionActivity(MainActivity.this, permissions);
                        } else {
                            Log.e(TAG, "获取权限失败");
                        }
                        Toast.makeText(getApplicationContext(), "file permission is lost", Toast.LENGTH_SHORT).show();
                    }
                });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        mSubG.tryClose();
        mSubG1.tryClose();
        mSubG2.tryClose();
        mSubG3.tryClose();
        mSubG4.tryClose();
        mSubG5.tryClose();

        mSendTask.destroy();
        mReceiveTask.destroy();
    }

    @Override
    public void onClick(View v) {
        if (v == mBtnSend) {
            mIsSend = !mIsSend;
            if (mIsSend) {
                try {
                    if (checkBox.isChecked()) {
                        mSubG = new SubG(SubG.SPIAUD_DEV_PATH + "0.0");
                        Log.e(TAG, "spi0");
                    }
                    if (checkBox1.isChecked()) {
                        mSubG1 = new SubG(SubG.SPIAUD_DEV_PATH + "1.0");
                        Log.e(TAG, "spi1");
                    }
                    if (checkBox2.isChecked()) {
                        mSubG2 = new SubG(SubG.SPIAUD_DEV_PATH + "2.0");
                        Log.e(TAG, "spi2");
                    }
                    if (checkBox3.isChecked()) {
                        mSubG3 = new SubG(SubG.SPIAUD_DEV_PATH + "3.0");
                        Log.e(TAG, "spi3");
                    }
                    if (checkBox4.isChecked()) {
                        mSubG4 = new SubG(SubG.SPIAUD_DEV_PATH + "4.0");
                        Log.e(TAG, "spi4");
                    }
                    if (checkBox5.isChecked()) {
                        mSubG5 = new SubG(SubG.SPIAUD_DEV_PATH + "5.0");
                        Log.e(TAG, "spi5");
                    }
                } catch (IOException e) {
                    Toast.makeText(this, "spi open failed", Toast.LENGTH_SHORT).show();
                    //mBtnSend.setEnabled(false);
                    //mBtnReceive.setEnabled(false);
                    e.printStackTrace();
                }
                mBtnSend.setText("停止发送");
                /*mTextReceive0.setText("");
                mTextReceive1.setText("");
                mTextReceive2.setText("");
                mTextReceive3.setText("");
                mTextReceive4.setText("");
                mTextReceive5.setText("");*/
                //mTextSend.setText("");
                //mTextloss.setText("");
                //mBtnReceive.setEnabled(false);

                mFirstSend = true;
                mTLinkIndex = 1;
                mTLinkIndex1 = 1;
                mTLinkIndex2 = 1;
                mTLinkIndex3 = 1;
                mTLinkIndex4 = 1;
                mTLinkIndex5 = 1;


                mTLinkspiuptotal = 0;
                mTLinkspiuplastIndex = 0;
                mTLinkspiuplosstotal = 0;
                mTLinkspidownlosstotal = 0;
                Arrays.fill(zhilingindex, 1);
                mTLinkindexdownlosstotal = 0;

                mTLinkspiuptotal1 = 0;
                mTLinkspiuplastIndex1 = 0;
                mTLinkspiuplosstotal1 = 0;
                mTLinkspidownlosstotal1 = 0;
                Arrays.fill(zhilingindex1, 1);
                mTLinkindexdownlosstotal1 = 0;

                mTLinkspiuptotal2 = 0;
                mTLinkspiuplastIndex2 = 0;
                mTLinkspiuplosstotal2 = 0;
                mTLinkspidownlosstotal2 = 0;
                Arrays.fill(zhilingindex2, 1);
                mTLinkindexdownlosstotal2 = 0;

                mTLinkspiuptotal3 = 0;
                mTLinkspiuplastIndex3 = 0;
                mTLinkspiuplosstotal3 = 0;
                mTLinkspidownlosstotal3 = 0;
                Arrays.fill(zhilingindex3, 1);
                mTLinkindexdownlosstotal3 = 0;

                mTLinkspiuptotal4 = 0;
                mTLinkspiuplastIndex4 = 0;
                mTLinkspiuplosstotal4 = 0;
                mTLinkspidownlosstotal4 = 0;
                Arrays.fill(zhilingindex4, 1);
                mTLinkindexdownlosstotal4 = 0;

                mTLinkspiuptotal5 = 0;
                mTLinkspiuplastIndex5 = 0;
                mTLinkspiuplosstotal5 = 0;
                mTLinkspidownlosstotal5 = 0;
                Arrays.fill(zhilingindex5, 1);
                mTLinkindexdownlosstotal5 = 0;

                mTLinkRFIndex = 0;
                mTLinkRFlosstotal = 0;
                localrfrssi = 0;
                romoterfrssi = 0;

                mTLinkRFIndex1 = 0;
                mTLinkRFlosstotal1 = 0;
                localrfrssi1 = 0;
                romoterfrssi1 = 0;

                mTLinkRFIndex2 = 0;
                mTLinkRFlosstotal2 = 0;
                localrfrssi2 = 0;
                romoterfrssi2 = 0;

                mTLinkRFIndex3 = 0;
                mTLinkRFlosstotal3 = 0;
                localrfrssi3 = 0;
                romoterfrssi3 = 0;

                mTLinkRFIndex4 = 0;
                mTLinkRFlosstotal4 = 0;
                localrfrssi4 = 0;
                romoterfrssi4 = 0;

                mTLinkRFIndex5 = 0;
                mTLinkRFlosstotal5 = 0;
                localrfrssi5 = 0;
                romoterfrssi5 = 0;

                gstorage.getStorage().clear();
                gstorage1.getStorage().clear();
                Arrays.fill(mTLinkindexuptotal, 0);
                Arrays.fill(mTLinkindexuplastIndex, 0);
                mTLinkindexuplosstotal = 0;
                Arrays.fill(mTLinkindexuptotal1, 0);
                Arrays.fill(mTLinkindexuplastIndex1, 0);
                mTLinkindexuplosstotal1 = 0;
                Arrays.fill(mTLinkindexuptotal2, 0);
                Arrays.fill(mTLinkindexuplastIndex2, 0);
                mTLinkindexuplosstotal2 = 0;
                Arrays.fill(mTLinkindexuptotal3, 0);
                Arrays.fill(mTLinkindexuplastIndex3, 0);
                mTLinkindexuplosstotal3 = 0;
                Arrays.fill(mTLinkindexuptotal4, 0);
                Arrays.fill(mTLinkindexuplastIndex4, 0);
                mTLinkindexuplosstotal4 = 0;
                Arrays.fill(mTLinkindexuptotal5, 0);
                Arrays.fill(mTLinkindexuplastIndex5, 0);
                mTLinkindexuplosstotal5 = 0;


                if (mSendMicOrFile) {
                    Log.e(TAG, "teric1");
                    mAudioRecord.startRecording();
                } else {
                    Log.e(TAG, "teric2");
                    openSendFile();
                    if (checkBox.isChecked()) {
                        mSendTask.setDelayMilliSeconds(getSendInterval());
                        mSendTask.start();
                    }
                    if (checkBox1.isChecked()) {
                        mSendTask1.setDelayMilliSeconds(getSendInterval());
                        mSendTask1.start();
                    }
                    if (checkBox2.isChecked()) {
                        mSendTask2.setDelayMilliSeconds(getSendInterval());
                        mSendTask2.start();
                    }
                    if (checkBox3.isChecked()) {
                        mSendTask3.setDelayMilliSeconds(getSendInterval());
                        mSendTask3.start();
                    }
                    if (checkBox4.isChecked()) {
                        mSendTask4.setDelayMilliSeconds(getSendInterval());
                        mSendTask4.start();
                    }
                    if (checkBox5.isChecked()) {
                        mSendTask5.setDelayMilliSeconds(getSendInterval());
                        mSendTask5.start();
                    }
                }
            } else {
                mBtnSend.setText("发送");
                if (mSendMicOrFile) {
                    mAudioRecord.stop();
                } else {

                }

                if (checkBox.isChecked()) {
                    mSendTask.stop();
                    mSubG.tryClose();
                }
                if (checkBox1.isChecked()) {
                    mSendTask1.stop();
                    mSubG1.tryClose();
                }
                if (checkBox2.isChecked()) {
                    mSendTask2.stop();
                    mSubG2.tryClose();
                }
                if (checkBox3.isChecked()) {
                    mSendTask3.stop();
                    mSubG3.tryClose();
                }
                if (checkBox4.isChecked()) {
                    mSendTask4.stop();
                    mSubG4.tryClose();
                }
                if (checkBox5.isChecked()) {
                    mSendTask5.stop();
                    mSubG5.tryClose();
                }
                closeSendFile();
            }
        } else if (v == mBtnReceive) {
            mIsReceive = !mIsReceive;
            if (mIsReceive) {
                //mTextReceive.setText("");
                openReceiveFile();
                mBtnReceive.setText("停止录音");
                //mBtnSend.setEnabled(false);
                //mFirstReceive = true;
                //mReceiveTask.start();
            } else {
                mBtnReceive.setText("录音");
                //mReceiveTask.stop();
                closeReceiveFile();
                //mStreamAudioTrack.flush();
            }
        } else if (v == mBtnPair) {
            Arrays.fill(mStartPair, true);
        } else if (v == mBtncancelPair) {
            Arrays.fill(mCancelPair, true);
        } else if (v == mBtnsendText) {
            Arrays.fill(mSendText, true);
        } else if (v == mBtnVersion) {
            Arrays.fill(mVersion, true);
        } else if (v == mBtnUpgrade) {
            Arrays.fill(mUpgrade, true);
        } else if (v == mBtnDut) {
            Arrays.fill(mDut, true);
        } else if (v == mBtnsubVersion) {
            Arrays.fill(msubVersion, true);
        } else if (v == mBtnsubUpgrade) {
            Arrays.fill(msubUpgrade, true);
        } else if (v == mBtnsubDut) {
            Arrays.fill(msubDut, true);
        } else if (v == mBtnsaystop) {
            mTstopsay = !mTstopsay;
            if (mTstopsay) {
                mBtnsaystop.setText("禁言");
            } else {
                mBtnsaystop.setText("抢麦");
            }
        } else if (v == mBtnupbag) {
            mTupbag = !mTupbag;
            if (mTupbag) {
                mBtnupbag.setText("不上报心跳");
            } else {
                mBtnupbag.setText("上报心跳包");
            }
        } else if (v == mBtnfirepair) {
            Arrays.fill(fourthstatus, 5);
        } else if (v == mBtnclearlog) {
            Arrays.fill(mTLclearlog, true);
        }
    }

    private void sendData() throws Exception {
        Log.e(TAG, "teric99 sendData");
        if (!mSendMicOrFile && mFIS != null) {
            int count;
            Arrays.fill(mAudioData, (byte) 0);
            count = mFIS.read(mAudioData);
            if (count != 720) {
                Arrays.fill(mAudioData, (byte) 0);
                count = 720;
                resetfileopen = 1;
            }
            if (true) {
                Arrays.fill(mFrameData, (byte) 0);
                {
                    //header
                    mFrameData[0] = (byte) 0x00;
                    mFrameData[1] = (byte) 0x00;
                    mFrameData[2] = (byte) 0xaa;
                    mFrameData[3] = (byte) 0xbb;
                    //cmd
                    mFrameData[4] = 0x01;
                    //len
                    mFrameData[5] = (byte) (743 & 0xff);
                    mFrameData[6] = (byte) ((743 >> 8) & 0xff);
                    //chip id //ff ff ff ff ff ff
                    if (mTstopsay) {
                        mFrameData[7] = (byte) 0xff;
                        mFrameData[8] = (byte) 0xff;
                        mFrameData[9] = (byte) 0xff;
                        mFrameData[10] = (byte) 0xff;
                        mFrameData[11] = (byte) 0xff;
                        mFrameData[12] = (byte) 0xff;
                    } else {
                        mFrameData[7] = 0;
                        mFrameData[8] = 0;
                        mFrameData[9] = 0;
                        mFrameData[10] = 0;
                        mFrameData[11] = 0;
                        mFrameData[12] = 0;
                    }
                    //device id
                    mFrameData[13] = 0;
                    mFrameData[14] = 0;

                    // sy info
                    mFrameData[15] = (byte) 0x0f;//(byte) (((firsthighstatus << 4) |  firstlowstatus)&0xff);
                    mFrameData[16] = (byte) (((secondhighstatus << 4) | secondlowstatus) & 0xff);
                    if (mTupbag) {
                        mFrameData[17] = (byte) (((1 << 7) + (1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    } else {
                        mFrameData[17] = (byte) (((1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    }
                    mFrameData[18] = (byte) (fourthstatus[0] & 0xff);
                    if (fourthstatus[0] == 5) {
                        fourthstatus[0] = 0;
                    }
                    mFrameData[19] = (byte) (fivethstatus & 0xff);
                    ;
                    mFrameData[20] = (byte) 0x01;
                    mFrameData[21] = (byte) 0x01;
                    mFrameData[22] = (byte) 0x01;
                    mFrameData[23] = (byte) 0x01;
                    mFrameData[24] = (byte) 0x01;
                    //language
                    mFrameData[25] = 0;
                    mFrameData[26] = 0;
                    //index
                    mFrameData[27] = (byte) (mTLinkIndex & 0xff);
                    mFrameData[28] = (byte) ((mTLinkIndex >> 8) & 0xff);
                    //audio data
                    System.arraycopy(mAudioData, 0, mFrameData, 29, 720);
                    //sum
                    int sumInt = calChecksum(mFrameData, 4, 748);
                    mFrameData[749] = (byte) (sumInt & 0xff);
                    mTLinkIndex++;
                    int lens = 0;
                    if (mStartPair[0] == true) {
                        mFrameData[750] = (byte) 0xaa;
                        mFrameData[751] = (byte) 0xbb;
                        //cmd
                        mFrameData[752] = (byte) 0x10;
                        //len
                        mFrameData[753] = (byte) (4 & 0xff);
                        mFrameData[754] = 0;
                        //index number
                        mFrameData[755] = (byte) (zhilingindex[0] & 0xff);
                        mFrameData[756] = (byte) ((zhilingindex[0] >> 8) & 0xff);
                        //value
                        mFrameData[757] = (byte) 0x01;
                        //checksum
                        int sumInt2 = calChecksum(mFrameData, 752, 757);
                        mFrameData[758] = (byte) (sumInt2 & 0xff);
                        zhilingindex[0]++;
                        mStartPair[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + " 配对"));
                        SpiLog.print("spi10", "配对下    " + ByteArraysToString(mFrameData, 750, 758));
                        lens += 9;
                    } else if (mCancelPair[0] == true) {
                        mFrameData[750] = (byte) 0xaa;
                        mFrameData[751] = (byte) 0xbb;
                        //cmd
                        mFrameData[752] = (byte) 0x10;
                        //len
                        mFrameData[753] = (byte) (4 & 0xff);
                        mFrameData[754] = 0;
                        //index number
                        mFrameData[755] = (byte) (zhilingindex[0] & 0xff);
                        mFrameData[756] = (byte) ((zhilingindex[0] >> 8) & 0xff);
                        //value
                        mFrameData[757] = (byte) 0x02;
                        //checksum
                        int sumInt0 = calChecksum(mFrameData, 752, 757);
                        mFrameData[758] = (byte) (sumInt0 & 0xff);
                        zhilingindex[0]++;
                        mCancelPair[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "退出配对"));
                        SpiLog.print("spi10", "退出配对下" + ByteArraysToString(mFrameData, 750, 758));
                        lens += 9;
                    }

                    if (mSendText[0] == true) {
                        mFrameData[749 + lens + 1] = (byte) 0xaa;
                        mFrameData[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData[749 + lens + 3] = (byte) 0x03;
                        //len
                        mFrameData[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData[749 + lens + 5] = 0;
                        //index number
                        mFrameData[749 + lens + 6] = (byte) (zhilingindex[1] & 0xff);
                        mFrameData[749 + lens + 7] = (byte) ((zhilingindex[1] >> 8) & 0xff);
                        //value
                        mFrameData[749 + lens + 8] = (byte) 0x01;
                        mFrameData[749 + lens + 9] = (byte) 0x01;
                        mFrameData[749 + lens + 10] = (byte) 0x01;
                        mFrameData[749 + lens + 11] = (byte) 0x01;

                        mFrameData[749 + lens + 12] = 0;
                        // data
                        mFrameData[749 + lens + 13] = (byte) 0xe;
                        mFrameData[749 + lens + 14] = (byte) 0xf;
                        //checksum

                        int sumInt1 = calChecksum(mFrameData, 749 + lens + 3, 749 + lens + 14);
                        mFrameData[749 + lens + 15] = (byte) (sumInt1 & 0xff);
                        zhilingindex[1]++;
                        mSendText[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "字幕"));
                        SpiLog.print("spi10", "字幕下    " + ByteArraysToString(mFrameData, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mVersion[0] == true) {
                        mFrameData[749 + lens + 1] = (byte) 0xaa;
                        mFrameData[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData[749 + lens + 3] = (byte) 0x20;
                        //len
                        mFrameData[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData[749 + lens + 5] = 0;
                        //index number
                        mFrameData[749 + lens + 6] = (byte) (zhilingindex[2] & 0xff);
                        mFrameData[749 + lens + 7] = (byte) ((zhilingindex[2] >> 8) & 0xff);
                        //value
                        mFrameData[749 + lens + 8] = 0;
                        //checksum
                        int sumInt3 = calChecksum(mFrameData, 749 + lens + 3, 749 + lens + 8);
                        mFrameData[749 + lens + 9] = (byte) (sumInt3 & 0xff);
                        zhilingindex[2]++;
                        mVersion[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "版本"));
                        SpiLog.print("spi10", "版本下    " + ByteArraysToString(mFrameData, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;

                    }
                    if (mUpgrade[0] == true) {
                        mFrameData[749 + lens + 1] = (byte) 0xaa;
                        mFrameData[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData[749 + lens + 3] = (byte) 0x21;
                        //len
                        mFrameData[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData[749 + lens + 5] = 0;
                        //index number
                        mFrameData[749 + lens + 6] = (byte) (zhilingindex[3] & 0xff);
                        mFrameData[749 + lens + 7] = (byte) ((zhilingindex[3] >> 8) & 0xff);
                        //value
                        mFrameData[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt4 = calChecksum(mFrameData, 749 + lens + 3, 749 + lens + 8);
                        mFrameData[749 + lens + 9] = (byte) (sumInt4 & 0xff);
                        zhilingindex[3]++;
                        mUpgrade[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "升级模式"));
                        SpiLog.print("spi10", "升级下    " + ByteArraysToString(mFrameData, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;

                    }
                    if (mDut[0] == true) {
                        mFrameData[749 + lens + 1] = (byte) 0xaa;
                        mFrameData[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData[749 + lens + 3] = (byte) 0x22;
                        //len
                        mFrameData[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData[749 + lens + 5] = 0;
                        //index number
                        mFrameData[749 + lens + 6] = (byte) (zhilingindex[4] & 0xff);
                        mFrameData[749 + lens + 7] = (byte) ((zhilingindex[4] >> 8) & 0xff);
                        //value
                        mFrameData[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt5 = calChecksum(mFrameData, 749 + lens + 3, 749 + lens + 8);
                        mFrameData[749 + lens + 9] = (byte) (sumInt5 & 0xff);
                        zhilingindex[4]++;
                        mDut[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "DUT模式"));
                        SpiLog.print("spi10", "DUT下     " + ByteArraysToString(mFrameData, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (msubVersion[0] == true) {//setting sub device
                        mFrameData[749 + lens + 1] = (byte) 0xaa;
                        mFrameData[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData[749 + lens + 3] = (byte) 0x30;
                        //len
                        mFrameData[749 + lens + 4] = (byte) (11 & 0xff);
                        mFrameData[749 + lens + 5] = 0;
                        //index number
                        mFrameData[749 + lens + 6] = (byte) (zhilingindex[5] & 0xff);
                        mFrameData[749 + lens + 7] = (byte) ((zhilingindex[5] >> 8) & 0xff);
                        //mac
                        mFrameData[749 + lens + 8] = (byte) nowsubMac[0];
                        mFrameData[749 + lens + 9] = (byte) nowsubMac[1];
                        mFrameData[749 + lens + 10] = (byte) nowsubMac[2];
                        mFrameData[749 + lens + 11] = (byte) nowsubMac[3];
                        mFrameData[749 + lens + 12] = (byte) nowsubMac[4];
                        mFrameData[749 + lens + 13] = (byte) nowsubMac[5];
                        //device id
                        mFrameData[749 + lens + 14] = 0x01;
                        mFrameData[749 + lens + 15] = 0x02;
                        //checksum
                        int sumInt6 = calChecksum(mFrameData, 749 + lens + 3, 749 + lens + 15);
                        mFrameData[749 + lens + 16] = (byte) (sumInt6 & 0xff);
                        zhilingindex[5]++;
                        msubVersion[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "子设ID"));
                        SpiLog.print("spi10", "子设ID下  " + ByteArraysToString(mFrameData, 749 + lens + 1, 749 + lens + 16));
                        lens += 16;
                    }
                    if (msubUpgrade[0] == true) {
                        mFrameData[749 + lens + 1] = (byte) 0xaa;
                        mFrameData[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData[749 + lens + 3] = (byte) 0x31;
                        //len
                        mFrameData[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData[749 + lens + 5] = 0;
                        //index number
                        mFrameData[749 + lens + 6] = (byte) (zhilingindex[6] & 0xff);
                        mFrameData[749 + lens + 7] = (byte) ((zhilingindex[6] >> 8) & 0xff);
                        //mac
                        mFrameData[749 + lens + 8] = (byte) nowsubMac[0];
                        mFrameData[749 + lens + 9] = (byte) nowsubMac[1];
                        mFrameData[749 + lens + 10] = (byte) nowsubMac[2];
                        mFrameData[749 + lens + 11] = (byte) nowsubMac[3];
                        mFrameData[749 + lens + 12] = (byte) nowsubMac[4];
                        mFrameData[749 + lens + 13] = (byte) nowsubMac[5];
                        //value
                        mFrameData[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt7 = calChecksum(mFrameData, 749 + lens + 3, 749 + lens + 14);
                        mFrameData[749 + lens + 15] = (byte) (sumInt7 & 0xff);

                        zhilingindex[6]++;
                        msubUpgrade[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "子机升级模式"));
                        SpiLog.print("spi10", "子升级下  " + ByteArraysToString(mFrameData, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (msubDut[0] == true) {//settting role
                        mFrameData[749 + lens + 1] = (byte) 0xaa;
                        mFrameData[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData[749 + lens + 3] = (byte) 0x32;
                        //len
                        mFrameData[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData[749 + lens + 5] = 0;
                        //index number
                        mFrameData[749 + lens + 6] = (byte) (zhilingindex[7] & 0xff);
                        mFrameData[749 + lens + 7] = (byte) ((zhilingindex[7] >> 8) & 0xff);
                        //value
                        mFrameData[749 + lens + 8] = (byte) nowsubMac[0];
                        mFrameData[749 + lens + 9] = (byte) nowsubMac[1];
                        mFrameData[749 + lens + 10] = (byte) nowsubMac[2];
                        mFrameData[749 + lens + 11] = (byte) nowsubMac[3];
                        mFrameData[749 + lens + 12] = (byte) nowsubMac[4];
                        mFrameData[749 + lens + 13] = (byte) nowsubMac[5];
                        //value
                        mFrameData[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt8 = calChecksum(mFrameData, 749 + lens + 3, 749 + lens + 14);
                        mFrameData[749 + lens + 15] = (byte) (sumInt8 & 0xff);
                        zhilingindex[7]++;
                        msubDut[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "子设角色"));
                        SpiLog.print("spi10", "子设角色下" + ByteArraysToString(mFrameData, 749 + lens + 1, 749 + lens + 15));
                        //String xxx = ByteArraysToString(mFrameData,749 + lens + 1,749 + lens + 15);
                        //Log.e(TAG,"teric0123="+xxx);
                        lens += 15;

                    }
                    if (mTLclearlog[0] == true) {
                        mFrameData[749 + lens + 1] = (byte) 0xaa;
                        mFrameData[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData[749 + lens + 3] = (byte) 0x4f;
                        //len
                        mFrameData[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData[749 + lens + 5] = 0;
                        //index number
                        mFrameData[749 + lens + 6] = (byte) (zhilingindex[8] & 0xff);
                        mFrameData[749 + lens + 7] = (byte) ((zhilingindex[8] >> 8) & 0xff);
                        //value
                        mFrameData[749 + lens + 8] = 0x01;
                        //checksum
                        int sumInt9 = calChecksum(mFrameData, 749 + lens + 3, 749 + lens + 8);
                        mFrameData[749 + lens + 9] = (byte) (sumInt9 & 0xff);
                        zhilingindex[8]++;
                        mTLclearlog[0] = false;
                        runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "清TL log"));
                        SpiLog.print("spi10", "清log下   " + ByteArraysToString(mFrameData, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }

                }
                //int xxx1 = (mFrameData[17]&0xff);
                //int xxx2 = (mFrameData[18]&0xff);
                //Log.i(TAG,"17="+xxx1 +"=18="+xxx2+ "  mFrameData teric count = " + mFrameData.length + ", " + Arrays.toString(mFrameData));
                Arrays.fill(mReceiveData, (byte) 0);
                mReceiveData = mSubG.trySpiTransfer(mFrameData);
                receiveData();
                if (mFirstSend) {
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime()));
                    mFirstSend = false;
                }
                if (resetfileopen == 1) {
                    closeSendFile();
                    openSendFile();
                    resetfileopen = 0;
                }
            }
        }
    }

    private int calChecksum(byte[] data, int begins, int ends) {
        int sum = 0;
        for (int i = begins; i <= ends; i++) {
            sum = (sum + data[i] & 0xFF) & 0xFF;
        }

        return sum;
    }

    private void sendData1() throws Exception {
        Log.e(TAG, "teric99 sendData1");
        if (!mSendMicOrFile && mFIS1 != null) {
            int count;
            Arrays.fill(mAudioData1, (byte) 0);
            count = mFIS1.read(mAudioData1);
            if (count != 720) {
                Arrays.fill(mAudioData1, (byte) 0);
                count = 720;
                resetfileopen1 = 1;
            }
            if (true) {
                Arrays.fill(mFrameData1, (byte) 0);
                {
                    //header
                    mFrameData1[0] = (byte) 0x00;
                    mFrameData1[1] = (byte) 0x00;
                    mFrameData1[2] = (byte) 0xaa;
                    mFrameData1[3] = (byte) 0xbb;
                    //cmd
                    mFrameData1[4] = 0x01;
                    //len
                    mFrameData1[5] = (byte) (743 & 0xff);
                    mFrameData1[6] = (byte) ((743 >> 8) & 0xff);
                    //chip id //ff ff ff ff ff ff
                    if (mTstopsay) {
                        mFrameData1[7] = (byte) 0xff;
                        mFrameData1[8] = (byte) 0xff;
                        mFrameData1[9] = (byte) 0xff;
                        mFrameData1[10] = (byte) 0xff;
                        mFrameData1[11] = (byte) 0xff;
                        mFrameData1[12] = (byte) 0xff;
                    } else {
                        mFrameData1[7] = 0;
                        mFrameData1[8] = 0;
                        mFrameData1[9] = 0;
                        mFrameData1[10] = 0;
                        mFrameData1[11] = 0;
                        mFrameData1[12] = 0;
                    }
                    //device id
                    mFrameData1[13] = 0;
                    mFrameData1[14] = 0;

                    // sy info
                    mFrameData1[15] = (byte) 0x0f;//(byte) (((firsthighstatus << 4) |  firstlowstatus)&0xff);
                    mFrameData1[16] = (byte) (((secondhighstatus << 4) | secondlowstatus) & 0xff);
                    if (mTupbag) {
                        mFrameData1[17] = (byte) (((1 << 7) + (1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    } else {
                        mFrameData1[17] = (byte) (((1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    }
                    mFrameData1[18] = (byte) (fourthstatus[1] & 0xff);
                    if (fourthstatus[1] == 5) {
                        fourthstatus[1] = 0;
                    }
                    mFrameData1[19] = (byte) (fivethstatus & 0xff);
                    ;
                    mFrameData1[20] = (byte) 0x01;
                    mFrameData1[21] = (byte) 0x01;
                    mFrameData1[22] = (byte) 0x01;
                    mFrameData1[23] = (byte) 0x01;
                    mFrameData1[24] = (byte) 0x01;
                    //language
                    mFrameData1[25] = 0;
                    mFrameData1[26] = 0;
                    //index
                    mFrameData1[27] = (byte) (mTLinkIndex1 & 0xff);
                    mFrameData1[28] = (byte) ((mTLinkIndex1 >> 8) & 0xff);
                    //audio data
                    System.arraycopy(mAudioData1, 0, mFrameData1, 29, 720);
                    //sum
                    int sumInt = calChecksum(mFrameData1, 4, 748);
                    mFrameData1[749] = (byte) (sumInt & 0xff);
                    mTLinkIndex1++;
                    int lens = 0;

                    if (mStartPair[1] == true) {
                        mFrameData1[750] = (byte) 0xaa;
                        mFrameData1[751] = (byte) 0xbb;
                        //cmd
                        mFrameData1[752] = (byte) 0x10;
                        //len
                        mFrameData1[753] = (byte) (4 & 0xff);
                        mFrameData1[754] = 0;
                        //index number
                        mFrameData1[755] = (byte) (zhilingindex1[0] & 0xff);
                        mFrameData1[756] = (byte) ((zhilingindex1[0] >> 8) & 0xff);
                        //value
                        mFrameData1[757] = (byte) 0x01;
                        //checksum
                        int sumInt2 = calChecksum(mFrameData1, 752, 757);
                        mFrameData1[758] = (byte) (sumInt2 & 0xff);
                        zhilingindex1[0]++;
                        mStartPair[1] = false;
                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + " 配对"));
                        SpiLog.print("spi11", "配对下    " + ByteArraysToString(mFrameData1, 750, 758));
                        lens += 9;
                    } else if (mCancelPair[1] == true) {
                        mFrameData1[750] = (byte) 0xaa;
                        mFrameData1[751] = (byte) 0xbb;
                        //cmd
                        mFrameData1[752] = (byte) 0x10;
                        //len
                        mFrameData1[753] = (byte) (4 & 0xff);
                        mFrameData1[754] = 0;
                        //index number
                        mFrameData1[755] = (byte) (zhilingindex1[0] & 0xff);
                        mFrameData1[756] = (byte) ((zhilingindex1[0] >> 8) & 0xff);
                        //value
                        mFrameData1[757] = (byte) 0x02;
                        //checksum
                        int sumInt0 = calChecksum(mFrameData1, 752, 757);
                        mFrameData1[758] = (byte) (sumInt0 & 0xff);
                        zhilingindex1[0]++;
                        mCancelPair[1] = false;
                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "退出配对"));
                        SpiLog.print("spi11", "退出配对下" + ByteArraysToString(mFrameData1, 750, 758));
                        lens += 9;
                    }

                    if (mSendText[1] == true) {
                        mFrameData1[749 + lens + 1] = (byte) 0xaa;
                        mFrameData1[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData1[749 + lens + 3] = (byte) 0x03;
                        //len
                        mFrameData1[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData1[749 + lens + 5] = 0;
                        //index number
                        mFrameData1[749 + lens + 6] = (byte) (zhilingindex1[1] & 0xff);
                        mFrameData1[749 + lens + 7] = (byte) ((zhilingindex1[1] >> 8) & 0xff);
                        //value
                        mFrameData1[749 + lens + 8] = (byte) 0x01;
                        mFrameData1[749 + lens + 9] = (byte) 0x01;
                        mFrameData1[749 + lens + 10] = (byte) 0x01;
                        mFrameData1[749 + lens + 11] = (byte) 0x01;

                        mFrameData1[749 + lens + 12] = 0;
                        // data
                        mFrameData1[749 + lens + 13] = (byte) 0xe;
                        mFrameData1[749 + lens + 14] = (byte) 0xf;
                        //checksum

                        int sumInt1 = calChecksum(mFrameData1, 749 + lens + 3, 749 + lens + 14);
                        mFrameData1[749 + lens + 15] = (byte) (sumInt1 & 0xff);
                        zhilingindex1[1]++;
                        mSendText[1] = false;
                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "字幕"));
                        SpiLog.print("spi11", "字幕下    " + ByteArraysToString(mFrameData1, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mVersion[1] == true) {
                        mFrameData1[749 + lens + 1] = (byte) 0xaa;
                        mFrameData1[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData1[749 + lens + 3] = (byte) 0x20;
                        //len
                        mFrameData1[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData1[749 + lens + 5] = 0;
                        //index number
                        mFrameData1[749 + lens + 6] = (byte) (zhilingindex1[2] & 0xff);
                        mFrameData1[749 + lens + 7] = (byte) ((zhilingindex1[2] >> 8) & 0xff);
                        //value
                        mFrameData1[749 + lens + 8] = 0;
                        //checksum
                        int sumInt3 = calChecksum(mFrameData1, 749 + lens + 3, 749 + lens + 8);
                        mFrameData1[749 + lens + 9] = (byte) (sumInt3 & 0xff);
                        zhilingindex1[2]++;
                        mVersion[1] = false;

                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "版本"));
                        SpiLog.print("spi11", "版本下    " + ByteArraysToString(mFrameData1, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mUpgrade[1] == true) {
                        mFrameData1[749 + lens + 1] = (byte) 0xaa;
                        mFrameData1[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData1[749 + lens + 3] = (byte) 0x21;
                        //len
                        mFrameData1[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData1[749 + lens + 5] = 0;
                        //index number
                        mFrameData1[749 + lens + 6] = (byte) (zhilingindex1[3] & 0xff);
                        mFrameData1[749 + lens + 7] = (byte) ((zhilingindex1[3] >> 8) & 0xff);
                        //value
                        mFrameData1[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt4 = calChecksum(mFrameData1, 749 + lens + 3, 749 + lens + 8);
                        mFrameData1[749 + lens + 9] = (byte) (sumInt4 & 0xff);
                        zhilingindex1[3]++;
                        mUpgrade[1] = false;

                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "升级模式"));
                        SpiLog.print("spi11", "升级下    " + ByteArraysToString(mFrameData1, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mDut[1] == true) {
                        mFrameData1[749 + lens + 1] = (byte) 0xaa;
                        mFrameData1[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData1[749 + lens + 3] = (byte) 0x22;
                        //len
                        mFrameData1[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData1[749 + lens + 5] = 0;
                        //index number
                        mFrameData1[749 + lens + 6] = (byte) (zhilingindex1[4] & 0xff);
                        mFrameData1[749 + lens + 7] = (byte) ((zhilingindex1[4] >> 8) & 0xff);
                        //value
                        mFrameData1[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt5 = calChecksum(mFrameData1, 749 + lens + 3, 749 + lens + 8);
                        mFrameData1[749 + lens + 9] = (byte) (sumInt5 & 0xff);
                        zhilingindex1[4]++;
                        mDut[1] = false;

                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "DUT模式"));
                        SpiLog.print("spi11", "DUT下     " + ByteArraysToString(mFrameData1, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (msubVersion[1] == true) {//setting sub device
                        mFrameData1[749 + lens + 1] = (byte) 0xaa;
                        mFrameData1[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData1[749 + lens + 3] = (byte) 0x30;
                        //len
                        mFrameData1[749 + lens + 4] = (byte) (11 & 0xff);
                        mFrameData1[749 + lens + 5] = 0;
                        //index number
                        mFrameData1[749 + lens + 6] = (byte) (zhilingindex1[5] & 0xff);
                        mFrameData1[749 + lens + 7] = (byte) ((zhilingindex1[5] >> 8) & 0xff);
                        //mac
                        mFrameData1[749 + lens + 8] = (byte) nowsubMac1[0];
                        mFrameData1[749 + lens + 9] = (byte) nowsubMac1[1];
                        mFrameData1[749 + lens + 10] = (byte) nowsubMac1[2];
                        mFrameData1[749 + lens + 11] = (byte) nowsubMac1[3];
                        mFrameData1[749 + lens + 12] = (byte) nowsubMac1[4];
                        mFrameData1[749 + lens + 13] = (byte) nowsubMac1[5];
                        //device id
                        mFrameData1[749 + lens + 14] = 0x01;
                        mFrameData1[749 + lens + 15] = 0x02;
                        //checksum
                        int sumInt6 = calChecksum(mFrameData1, 749 + lens + 3, 749 + lens + 15);
                        mFrameData1[749 + lens + 16] = (byte) (sumInt6 & 0xff);
                        zhilingindex1[5]++;
                        msubVersion[1] = false;

                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "子设ID"));
                        SpiLog.print("spi11", "子设ID下  " + ByteArraysToString(mFrameData1, 749 + lens + 1, 749 + lens + 16));
                        lens += 16;
                    }
                    if (msubUpgrade[1] == true) {
                        mFrameData1[749 + lens + 1] = (byte) 0xaa;
                        mFrameData1[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData1[749 + lens + 3] = (byte) 0x31;
                        //len
                        mFrameData1[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData1[749 + lens + 5] = 0;
                        //index number
                        mFrameData1[749 + lens + 6] = (byte) (zhilingindex1[6] & 0xff);
                        mFrameData1[749 + lens + 7] = (byte) ((zhilingindex1[6] >> 8) & 0xff);
                        //mac
                        mFrameData1[749 + lens + 8] = (byte) nowsubMac1[0];
                        mFrameData1[749 + lens + 9] = (byte) nowsubMac1[1];
                        mFrameData1[749 + lens + 10] = (byte) nowsubMac1[2];
                        mFrameData1[749 + lens + 11] = (byte) nowsubMac1[3];
                        mFrameData1[749 + lens + 12] = (byte) nowsubMac1[4];
                        mFrameData1[749 + lens + 13] = (byte) nowsubMac1[5];
                        //value
                        mFrameData1[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt7 = calChecksum(mFrameData1, 749 + lens + 3, 749 + lens + 14);
                        mFrameData1[749 + lens + 15] = (byte) (sumInt7 & 0xff);

                        zhilingindex1[6]++;
                        msubUpgrade[1] = false;

                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "子机升级模式"));
                        SpiLog.print("spi11", "子升级下  " + ByteArraysToString(mFrameData1, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;

                    }
                    if (msubDut[1] == true) {//settting role
                        mFrameData1[749 + lens + 1] = (byte) 0xaa;
                        mFrameData1[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData1[749 + lens + 3] = (byte) 0x32;
                        //len
                        mFrameData1[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData1[749 + lens + 5] = 0;
                        //index number
                        mFrameData1[749 + lens + 6] = (byte) (zhilingindex1[7] & 0xff);
                        mFrameData1[749 + lens + 7] = (byte) ((zhilingindex1[7] >> 8) & 0xff);
                        //value
                        mFrameData1[749 + lens + 8] = (byte) nowsubMac1[0];
                        mFrameData1[749 + lens + 9] = (byte) nowsubMac1[1];
                        mFrameData1[749 + lens + 10] = (byte) nowsubMac1[2];
                        mFrameData1[749 + lens + 11] = (byte) nowsubMac1[3];
                        mFrameData1[749 + lens + 12] = (byte) nowsubMac1[4];
                        mFrameData1[749 + lens + 13] = (byte) nowsubMac1[5];
                        //value
                        mFrameData1[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt8 = calChecksum(mFrameData1, 749 + lens + 3, 749 + lens + 14);
                        mFrameData1[749 + lens + 15] = (byte) (sumInt8 & 0xff);
                        zhilingindex1[7]++;
                        msubDut[1] = false;

                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "子设角色"));
                        SpiLog.print("spi11", "子设角色下" + ByteArraysToString(mFrameData1, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mTLclearlog[1] == true) {
                        mFrameData1[749 + lens + 1] = (byte) 0xaa;
                        mFrameData1[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData1[749 + lens + 3] = (byte) 0x4f;
                        //len
                        mFrameData1[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData1[749 + lens + 5] = 0;
                        //index number
                        mFrameData1[749 + lens + 6] = (byte) (zhilingindex1[8] & 0xff);
                        mFrameData1[749 + lens + 7] = (byte) ((zhilingindex1[8] >> 8) & 0xff);
                        //value
                        mFrameData1[749 + lens + 8] = 0x01;
                        //checksum
                        int sumInt9 = calChecksum(mFrameData1, 749 + lens + 3, 749 + lens + 8);
                        mFrameData1[749 + lens + 9] = (byte) (sumInt9 & 0xff);
                        zhilingindex1[8]++;
                        mTLclearlog[1] = false;
                        runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "清TL log"));
                        SpiLog.print("spi11", "清log下   " + ByteArraysToString(mFrameData1, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }

                }
                Arrays.fill(mReceiveData1, (byte) 0);
                mReceiveData1 = mSubG1.trySpiTransfer(mFrameData1);
                receiveData1();
                if (mFirstSend1) {
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime()));
                    mFirstSend1 = false;
                }
                if (resetfileopen1 == 1) {
                    closeSendFile();
                    openSendFile();
                    resetfileopen1 = 0;
                }
            }
        }
    }

    private void sendData2() throws Exception {
        Log.e(TAG, "teric99 sendData2");
        if (!mSendMicOrFile && mFIS2 != null) {
            int count;
            Arrays.fill(mAudioData2, (byte) 0);
            count = mFIS2.read(mAudioData2);
            if (count != 720) {
                Arrays.fill(mAudioData2, (byte) 0);
                count = 720;
                resetfileopen2 = 1;
            }
            if (true) {
                Arrays.fill(mFrameData2, (byte) 0);
                {
                    //header
                    mFrameData2[0] = (byte) 0x00;
                    mFrameData2[1] = (byte) 0x00;
                    mFrameData2[2] = (byte) 0xaa;
                    mFrameData2[3] = (byte) 0xbb;
                    //cmd
                    mFrameData2[4] = 0x01;
                    //len
                    mFrameData2[5] = (byte) (743 & 0xff);
                    mFrameData2[6] = (byte) ((743 >> 8) & 0xff);
                    //chip id //ff ff ff ff ff ff
                    if (mTstopsay) {
                        mFrameData2[7] = (byte) 0xff;
                        mFrameData2[8] = (byte) 0xff;
                        mFrameData2[9] = (byte) 0xff;
                        mFrameData2[10] = (byte) 0xff;
                        mFrameData2[11] = (byte) 0xff;
                        mFrameData2[12] = (byte) 0xff;
                    } else {
                        mFrameData2[7] = 0;
                        mFrameData2[8] = 0;
                        mFrameData2[9] = 0;
                        mFrameData2[10] = 0;
                        mFrameData2[11] = 0;
                        mFrameData2[12] = 0;
                    }
                    //device id
                    mFrameData2[13] = 0;
                    mFrameData2[14] = 0;

                    // sy info
                    mFrameData2[15] = (byte) 0x0f;//(byte) (((firsthighstatus << 4) |  firstlowstatus)&0xff);
                    mFrameData2[16] = (byte) (((secondhighstatus << 4) | secondlowstatus) & 0xff);
                    if (mTupbag) {
                        mFrameData2[17] = (byte) (((1 << 7) + (1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    } else {
                        mFrameData2[17] = (byte) (((1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    }
                    mFrameData2[18] = (byte) (fourthstatus[2] & 0xff);
                    if (fourthstatus[2] == 5) {
                        fourthstatus[2] = 0;
                    }
                    mFrameData2[19] = (byte) (fivethstatus & 0xff);
                    ;
                    mFrameData2[20] = (byte) 0x01;
                    mFrameData2[21] = (byte) 0x01;
                    mFrameData2[22] = (byte) 0x01;
                    mFrameData2[23] = (byte) 0x01;
                    mFrameData2[24] = (byte) 0x01;
                    //language
                    mFrameData2[25] = 0;
                    mFrameData2[26] = 0;
                    //index
                    mFrameData2[27] = (byte) (mTLinkIndex2 & 0xff);
                    mFrameData2[28] = (byte) ((mTLinkIndex2 >> 8) & 0xff);
                    //audio data
                    System.arraycopy(mAudioData2, 0, mFrameData2, 29, 720);
                    //sum
                    int sumInt = calChecksum(mFrameData2, 4, 748);
                    mFrameData2[749] = (byte) (sumInt & 0xff);
                    mTLinkIndex2++;
                    int lens = 0;

                    if (mStartPair[2] == true) {
                        mFrameData2[750] = (byte) 0xaa;
                        mFrameData2[751] = (byte) 0xbb;
                        //cmd
                        mFrameData2[752] = (byte) 0x10;
                        //len
                        mFrameData2[753] = (byte) (4 & 0xff);
                        mFrameData2[754] = 0;
                        //index number
                        mFrameData2[755] = (byte) (zhilingindex2[0] & 0xff);
                        mFrameData2[756] = (byte) ((zhilingindex2[0] >> 8) & 0xff);
                        //value
                        mFrameData2[757] = (byte) 0x01;
                        //checksum
                        int sumInt2 = calChecksum(mFrameData2, 752, 757);
                        mFrameData2[758] = (byte) (sumInt2 & 0xff);
                        zhilingindex2[0]++;
                        mStartPair[2] = false;
                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + " 配对"));
                        SpiLog.print("spi12", "配对下    " + ByteArraysToString(mFrameData2, 750, 758));
                        lens += 9;
                    } else if (mCancelPair[2] == true) {
                        mFrameData2[750] = (byte) 0xaa;
                        mFrameData2[751] = (byte) 0xbb;
                        //cmd
                        mFrameData2[752] = (byte) 0x10;
                        //len
                        mFrameData2[753] = (byte) (4 & 0xff);
                        mFrameData2[754] = 0;
                        //index number
                        mFrameData2[755] = (byte) (zhilingindex2[0] & 0xff);
                        mFrameData2[756] = (byte) ((zhilingindex2[0] >> 8) & 0xff);
                        //value
                        mFrameData2[757] = (byte) 0x02;
                        //checksum
                        int sumInt0 = calChecksum(mFrameData2, 752, 757);
                        mFrameData2[758] = (byte) (sumInt0 & 0xff);
                        zhilingindex2[0]++;
                        mCancelPair[2] = false;
                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "退出配对"));
                        SpiLog.print("spi12", "退出配对下" + ByteArraysToString(mFrameData2, 750, 758));
                        lens += 9;
                    }
                    if (mSendText[2] == true) {
                        mFrameData2[749 + lens + 1] = (byte) 0xaa;
                        mFrameData2[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData2[749 + lens + 3] = (byte) 0x03;
                        //len
                        mFrameData2[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData2[749 + lens + 5] = 0;
                        //index number
                        mFrameData2[749 + lens + 6] = (byte) (zhilingindex2[1] & 0xff);
                        mFrameData2[749 + lens + 7] = (byte) ((zhilingindex2[1] >> 8) & 0xff);
                        //value
                        mFrameData2[749 + lens + 8] = (byte) 0x01;
                        mFrameData2[749 + lens + 9] = (byte) 0x01;
                        mFrameData2[749 + lens + 10] = (byte) 0x01;
                        mFrameData2[749 + lens + 11] = (byte) 0x01;

                        mFrameData2[749 + lens + 12] = 0;
                        // data
                        mFrameData2[749 + lens + 13] = (byte) 0xe;
                        mFrameData2[749 + lens + 14] = (byte) 0xf;
                        //checksum

                        int sumInt1 = calChecksum(mFrameData2, 749 + lens + 3, 749 + lens + 14);
                        mFrameData2[749 + lens + 15] = (byte) (sumInt1 & 0xff);
                        zhilingindex2[1]++;
                        mSendText[2] = false;

                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "字幕"));
                        SpiLog.print("spi12", "字幕下    " + ByteArraysToString(mFrameData2, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mVersion[2] == true) {
                        mFrameData2[749 + lens + 1] = (byte) 0xaa;
                        mFrameData2[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData2[749 + lens + 3] = (byte) 0x20;
                        //len
                        mFrameData2[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData2[749 + lens + 5] = 0;
                        //index number
                        mFrameData2[749 + lens + 6] = (byte) (zhilingindex2[2] & 0xff);
                        mFrameData2[749 + lens + 7] = (byte) ((zhilingindex2[2] >> 8) & 0xff);
                        //value
                        mFrameData2[749 + lens + 8] = 0;
                        //checksum
                        int sumInt3 = calChecksum(mFrameData2, 749 + lens + 3, 749 + lens + 8);
                        mFrameData2[749 + lens + 9] = (byte) (sumInt3 & 0xff);
                        zhilingindex2[2]++;
                        mVersion[2] = false;

                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "版本"));
                        SpiLog.print("spi12", "版本下    " + ByteArraysToString(mFrameData2, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mUpgrade[2] == true) {
                        mFrameData2[749 + lens + 1] = (byte) 0xaa;
                        mFrameData2[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData2[749 + lens + 3] = (byte) 0x21;
                        //len
                        mFrameData2[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData2[749 + lens + 5] = 0;
                        //index number
                        mFrameData2[749 + lens + 6] = (byte) (zhilingindex2[3] & 0xff);
                        mFrameData2[749 + lens + 7] = (byte) ((zhilingindex2[3] >> 8) & 0xff);
                        //value
                        mFrameData2[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt4 = calChecksum(mFrameData2, 749 + lens + 3, 749 + lens + 8);
                        mFrameData2[749 + lens + 9] = (byte) (sumInt4 & 0xff);
                        zhilingindex2[3]++;
                        mUpgrade[2] = false;

                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "升级模式"));
                        SpiLog.print("spi12", "升级下    " + ByteArraysToString(mFrameData2, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mDut[2] == true) {
                        mFrameData2[749 + lens + 1] = (byte) 0xaa;
                        mFrameData2[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData2[749 + lens + 3] = (byte) 0x22;
                        //len
                        mFrameData2[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData2[749 + lens + 5] = 0;
                        //index number
                        mFrameData2[749 + lens + 6] = (byte) (zhilingindex2[4] & 0xff);
                        mFrameData2[749 + lens + 7] = (byte) ((zhilingindex2[4] >> 8) & 0xff);
                        //value
                        mFrameData2[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt5 = calChecksum(mFrameData2, 749 + lens + 3, 749 + lens + 8);
                        mFrameData2[749 + lens + 9] = (byte) (sumInt5 & 0xff);
                        zhilingindex2[4]++;
                        mDut[2] = false;

                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "DUT模式"));
                        SpiLog.print("spi12", "DUT下     " + ByteArraysToString(mFrameData2, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (msubVersion[2] == true) {//setting sub device
                        mFrameData2[749 + lens + 1] = (byte) 0xaa;
                        mFrameData2[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData2[749 + lens + 3] = (byte) 0x30;
                        //len
                        mFrameData2[749 + lens + 4] = (byte) (11 & 0xff);
                        mFrameData2[749 + lens + 5] = 0;
                        //index number
                        mFrameData2[749 + lens + 6] = (byte) (zhilingindex2[5] & 0xff);
                        mFrameData2[749 + lens + 7] = (byte) ((zhilingindex2[5] >> 8) & 0xff);
                        //mac
                        mFrameData2[749 + lens + 8] = (byte) nowsubMac2[0];
                        mFrameData2[749 + lens + 9] = (byte) nowsubMac2[1];
                        mFrameData2[749 + lens + 10] = (byte) nowsubMac2[2];
                        mFrameData2[749 + lens + 11] = (byte) nowsubMac2[3];
                        mFrameData2[749 + lens + 12] = (byte) nowsubMac2[4];
                        mFrameData2[749 + lens + 13] = (byte) nowsubMac2[5];
                        //device id
                        mFrameData2[749 + lens + 14] = 0x01;
                        mFrameData2[749 + lens + 15] = 0x02;
                        //checksum
                        int sumInt6 = calChecksum(mFrameData2, 749 + lens + 3, 749 + lens + 15);
                        mFrameData2[749 + lens + 16] = (byte) (sumInt6 & 0xff);
                        zhilingindex2[5]++;
                        msubVersion[2] = false;

                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "子设ID"));
                        SpiLog.print("spi12", "子设ID下  " + ByteArraysToString(mFrameData2, 749 + lens + 1, 749 + lens + 16));
                        lens += 16;
                    }
                    if (msubUpgrade[2] == true) {
                        mFrameData2[749 + lens + 1] = (byte) 0xaa;
                        mFrameData2[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData2[749 + lens + 3] = (byte) 0x31;
                        //len
                        mFrameData2[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData2[749 + lens + 5] = 0;
                        //index number
                        mFrameData2[749 + lens + 6] = (byte) (zhilingindex2[6] & 0xff);
                        mFrameData2[749 + lens + 7] = (byte) ((zhilingindex2[6] >> 8) & 0xff);
                        //mac
                        mFrameData2[749 + lens + 8] = (byte) nowsubMac2[0];
                        mFrameData2[749 + lens + 9] = (byte) nowsubMac2[1];
                        mFrameData2[749 + lens + 10] = (byte) nowsubMac2[2];
                        mFrameData2[749 + lens + 11] = (byte) nowsubMac2[3];
                        mFrameData2[749 + lens + 12] = (byte) nowsubMac2[4];
                        mFrameData2[749 + lens + 13] = (byte) nowsubMac2[5];
                        //value
                        mFrameData2[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt7 = calChecksum(mFrameData2, 749 + lens + 3, 749 + lens + 14);
                        mFrameData2[749 + lens + 15] = (byte) (sumInt7 & 0xff);

                        zhilingindex2[6]++;
                        msubUpgrade[2] = false;

                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "子机升级模式"));
                        SpiLog.print("spi12", "子升级下  " + ByteArraysToString(mFrameData2, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (msubDut[2] == true) {//settting role
                        mFrameData2[749 + lens + 1] = (byte) 0xaa;
                        mFrameData2[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData2[749 + lens + 3] = (byte) 0x32;
                        //len
                        mFrameData2[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData2[749 + lens + 5] = 0;
                        //index number
                        mFrameData2[749 + lens + 6] = (byte) (zhilingindex2[7] & 0xff);
                        mFrameData2[749 + lens + 7] = (byte) ((zhilingindex2[7] >> 8) & 0xff);
                        //value
                        mFrameData2[749 + lens + 8] = (byte) nowsubMac2[0];
                        mFrameData2[749 + lens + 9] = (byte) nowsubMac2[1];
                        mFrameData2[749 + lens + 10] = (byte) nowsubMac2[2];
                        mFrameData2[749 + lens + 11] = (byte) nowsubMac2[3];
                        mFrameData2[749 + lens + 12] = (byte) nowsubMac2[4];
                        mFrameData2[749 + lens + 13] = (byte) nowsubMac2[5];
                        //value
                        mFrameData2[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt8 = calChecksum(mFrameData2, 749 + lens + 3, 749 + lens + 14);
                        mFrameData2[749 + lens + 15] = (byte) (sumInt8 & 0xff);
                        zhilingindex2[7]++;
                        msubDut[2] = false;

                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "子设角色"));
                        SpiLog.print("spi12", "子设角色下" + ByteArraysToString(mFrameData2, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mTLclearlog[2] == true) {
                        mFrameData2[749 + lens + 1] = (byte) 0xaa;
                        mFrameData2[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData2[749 + lens + 3] = (byte) 0x4f;
                        //len
                        mFrameData2[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData2[749 + lens + 5] = 0;
                        //index number
                        mFrameData2[749 + lens + 6] = (byte) (zhilingindex2[8] & 0xff);
                        mFrameData2[749 + lens + 7] = (byte) ((zhilingindex2[8] >> 8) & 0xff);
                        //value
                        mFrameData2[749 + lens + 8] = 0x01;
                        //checksum
                        int sumInt9 = calChecksum(mFrameData2, 749 + lens + 3, 749 + lens + 8);
                        mFrameData2[749 + lens + 9] = (byte) (sumInt9 & 0xff);
                        zhilingindex2[8]++;

                        mTLclearlog[2] = false;
                        runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "清TL log"));
                        SpiLog.print("spi12", "清log下   " + ByteArraysToString(mFrameData2, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }

                }
                Arrays.fill(mReceiveData2, (byte) 0);
                mReceiveData2 = mSubG2.trySpiTransfer(mFrameData2);
                receiveData2();
                if (mFirstSend2) {
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime()));
                    mFirstSend2 = false;
                }
                if (resetfileopen2 == 1) {
                    closeSendFile();
                    openSendFile();
                    resetfileopen2 = 0;
                }
            }
        }
    }

    private void sendData3() throws Exception {
        Log.e(TAG, "sendData3");
        if (!mSendMicOrFile && mFIS3 != null) {
            int count;
            Arrays.fill(mAudioData3, (byte) 0);
            count = mFIS3.read(mAudioData3);
            if (count != 720) {
                Arrays.fill(mAudioData3, (byte) 0);
                count = 720;
                resetfileopen3 = 1;
            }
            if (true) {
                Arrays.fill(mFrameData3, (byte) 0);
                {
                    //header
                    mFrameData3[0] = (byte) 0x00;
                    mFrameData3[1] = (byte) 0x00;
                    mFrameData3[2] = (byte) 0xaa;
                    mFrameData3[3] = (byte) 0xbb;
                    //cmd
                    mFrameData3[4] = 0x01;
                    //len
                    mFrameData3[5] = (byte) (743 & 0xff);
                    mFrameData3[6] = (byte) ((743 >> 8) & 0xff);
                    //chip id //ff ff ff ff ff ff
                    if (mTstopsay) {
                        mFrameData3[7] = (byte) 0xff;
                        mFrameData3[8] = (byte) 0xff;
                        mFrameData3[9] = (byte) 0xff;
                        mFrameData3[10] = (byte) 0xff;
                        mFrameData3[11] = (byte) 0xff;
                        mFrameData3[12] = (byte) 0xff;
                    } else {
                        mFrameData3[7] = 0;
                        mFrameData3[8] = 0;
                        mFrameData3[9] = 0;
                        mFrameData3[10] = 0;
                        mFrameData3[11] = 0;
                        mFrameData3[12] = 0;
                    }
                    //device id
                    mFrameData3[13] = 0;
                    mFrameData3[14] = 0;

                    // sy info
                    mFrameData3[15] = (byte) 0x0f;//(byte) (((firsthighstatus << 4) |  firstlowstatus)&0xff);
                    mFrameData3[16] = (byte) (((secondhighstatus << 4) | secondlowstatus) & 0xff);
                    if (mTupbag) {
                        mFrameData3[17] = (byte) (((1 << 7) + (1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    } else {
                        mFrameData3[17] = (byte) (((1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    }
                    mFrameData3[18] = (byte) (fourthstatus[3] & 0xff);
                    if (fourthstatus[3] == 5) {
                        fourthstatus[3] = 0;
                    }
                    mFrameData3[19] = (byte) (fivethstatus & 0xff);
                    ;
                    mFrameData3[20] = (byte) 0x01;
                    mFrameData3[21] = (byte) 0x01;
                    mFrameData3[22] = (byte) 0x01;
                    mFrameData3[23] = (byte) 0x01;
                    mFrameData3[24] = (byte) 0x01;
                    //language
                    mFrameData3[25] = 0;
                    mFrameData3[26] = 0;
                    //index
                    mFrameData3[27] = (byte) (mTLinkIndex3 & 0xff);
                    mFrameData3[28] = (byte) ((mTLinkIndex3 >> 8) & 0xff);
                    //audio data
                    System.arraycopy(mAudioData3, 0, mFrameData3, 29, 720);
                    //sum
                    int sumInt = calChecksum(mFrameData3, 4, 748);
                    mFrameData3[749] = (byte) (sumInt & 0xff);
                    mTLinkIndex3++;
                    int lens = 0;

                    if (mStartPair[3] == true) {
                        mFrameData3[750] = (byte) 0xaa;
                        mFrameData3[751] = (byte) 0xbb;
                        //cmd
                        mFrameData3[752] = (byte) 0x10;
                        //len
                        mFrameData3[753] = (byte) (4 & 0xff);
                        mFrameData3[754] = 0;
                        //index number
                        mFrameData3[755] = (byte) (zhilingindex3[0] & 0xff);
                        mFrameData3[756] = (byte) ((zhilingindex3[0] >> 8) & 0xff);
                        //value
                        mFrameData3[757] = (byte) 0x01;
                        //checksum
                        int sumInt2 = calChecksum(mFrameData3, 752, 757);
                        mFrameData3[758] = (byte) (sumInt2 & 0xff);
                        zhilingindex3[0]++;
                        mStartPair[3] = false;
                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + " 配对"));
                        SpiLog.print("spi13", "配对下    " + ByteArraysToString(mFrameData3, 750, 758));
                        lens += 9;
                    } else if (mCancelPair[3] == true) {
                        mFrameData3[750] = (byte) 0xaa;
                        mFrameData3[751] = (byte) 0xbb;
                        //cmd
                        mFrameData3[752] = (byte) 0x10;
                        //len
                        mFrameData3[753] = (byte) (4 & 0xff);
                        mFrameData3[754] = 0;
                        //index number
                        mFrameData3[755] = (byte) (zhilingindex3[0] & 0xff);
                        mFrameData3[756] = (byte) ((zhilingindex3[0] >> 8) & 0xff);
                        //value
                        mFrameData3[757] = (byte) 0x02;
                        //checksum
                        int sumInt0 = calChecksum(mFrameData3, 752, 757);
                        mFrameData3[758] = (byte) (sumInt0 & 0xff);
                        zhilingindex3[0]++;
                        mCancelPair[3] = false;
                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "退出配对"));
                        SpiLog.print("spi13", "退出配对下" + ByteArraysToString(mFrameData3, 750, 758));
                        lens += 9;
                    }
                    if (mSendText[3] == true) {
                        mFrameData3[749 + lens + 1] = (byte) 0xaa;
                        mFrameData3[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData3[749 + lens + 3] = (byte) 0x03;
                        //len
                        mFrameData3[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData3[749 + lens + 5] = 0;
                        //index number
                        mFrameData3[749 + lens + 6] = (byte) (zhilingindex3[1] & 0xff);
                        mFrameData3[749 + lens + 7] = (byte) ((zhilingindex3[1] >> 8) & 0xff);
                        //value
                        mFrameData3[749 + lens + 8] = (byte) 0x01;
                        mFrameData3[749 + lens + 9] = (byte) 0x01;
                        mFrameData3[749 + lens + 10] = (byte) 0x01;
                        mFrameData3[749 + lens + 11] = (byte) 0x01;

                        mFrameData3[749 + lens + 12] = 0;
                        // data
                        mFrameData3[749 + lens + 13] = (byte) 0xe;
                        mFrameData3[749 + lens + 14] = (byte) 0xf;
                        //checksum

                        int sumInt1 = calChecksum(mFrameData3, 749 + lens + 3, 749 + lens + 14);
                        mFrameData3[749 + lens + 15] = (byte) (sumInt1 & 0xff);
                        zhilingindex3[1]++;
                        mSendText[3] = false;

                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "字幕"));
                        SpiLog.print("spi13", "字幕下    " + ByteArraysToString(mFrameData3, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mVersion[3] == true) {
                        mFrameData3[749 + lens + 1] = (byte) 0xaa;
                        mFrameData3[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData3[749 + lens + 3] = (byte) 0x20;
                        //len
                        mFrameData3[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData3[749 + lens + 5] = 0;
                        //index number
                        mFrameData3[749 + lens + 6] = (byte) (zhilingindex3[2] & 0xff);
                        mFrameData3[749 + lens + 7] = (byte) ((zhilingindex3[2] >> 8) & 0xff);
                        //value
                        mFrameData3[749 + lens + 8] = 0;
                        //checksum
                        int sumInt3 = calChecksum(mFrameData3, 749 + lens + 3, 749 + lens + 8);
                        mFrameData3[749 + lens + 9] = (byte) (sumInt3 & 0xff);
                        zhilingindex3[2]++;
                        mVersion[3] = false;

                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "版本"));
                        SpiLog.print("spi13", "版本下    " + ByteArraysToString(mFrameData3, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mUpgrade[3] == true) {
                        mFrameData3[749 + lens + 1] = (byte) 0xaa;
                        mFrameData3[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData3[749 + lens + 3] = (byte) 0x21;
                        //len
                        mFrameData3[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData3[749 + lens + 5] = 0;
                        //index number
                        mFrameData3[749 + lens + 6] = (byte) (zhilingindex3[3] & 0xff);
                        mFrameData3[749 + lens + 7] = (byte) ((zhilingindex3[3] >> 8) & 0xff);
                        //value
                        mFrameData3[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt4 = calChecksum(mFrameData3, 749 + lens + 3, 749 + lens + 8);
                        mFrameData3[749 + lens + 9] = (byte) (sumInt4 & 0xff);
                        zhilingindex3[3]++;
                        mUpgrade[3] = false;

                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "升级模式"));
                        SpiLog.print("spi13", "升级下    " + ByteArraysToString(mFrameData3, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mDut[3] == true) {
                        mFrameData3[749 + lens + 1] = (byte) 0xaa;
                        mFrameData3[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData3[749 + lens + 3] = (byte) 0x22;
                        //len
                        mFrameData3[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData3[749 + lens + 5] = 0;
                        //index number
                        mFrameData3[749 + lens + 6] = (byte) (zhilingindex3[4] & 0xff);
                        mFrameData3[749 + lens + 7] = (byte) ((zhilingindex3[4] >> 8) & 0xff);
                        //value
                        mFrameData3[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt5 = calChecksum(mFrameData3, 749 + lens + 3, 749 + lens + 8);
                        mFrameData3[749 + lens + 9] = (byte) (sumInt5 & 0xff);
                        zhilingindex3[4]++;
                        mDut[3] = false;

                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "DUT模式"));
                        SpiLog.print("spi13", "DUT下     " + ByteArraysToString(mFrameData3, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (msubVersion[3] == true) {//setting sub device
                        mFrameData3[749 + lens + 1] = (byte) 0xaa;
                        mFrameData3[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData3[749 + lens + 3] = (byte) 0x30;
                        //len
                        mFrameData3[749 + lens + 4] = (byte) (11 & 0xff);
                        mFrameData3[749 + lens + 5] = 0;
                        //index number
                        mFrameData3[749 + lens + 6] = (byte) (zhilingindex3[5] & 0xff);
                        mFrameData3[749 + lens + 7] = (byte) ((zhilingindex3[5] >> 8) & 0xff);
                        //mac
                        mFrameData3[749 + lens + 8] = (byte) nowsubMac3[0];
                        mFrameData3[749 + lens + 9] = (byte) nowsubMac3[1];
                        mFrameData3[749 + lens + 10] = (byte) nowsubMac3[2];
                        mFrameData3[749 + lens + 11] = (byte) nowsubMac3[3];
                        mFrameData3[749 + lens + 12] = (byte) nowsubMac3[4];
                        mFrameData3[749 + lens + 13] = (byte) nowsubMac3[5];
                        //device id
                        mFrameData3[749 + lens + 14] = 0x01;
                        mFrameData3[749 + lens + 15] = 0x02;
                        //checksum
                        int sumInt6 = calChecksum(mFrameData3, 749 + lens + 3, 749 + lens + 15);
                        mFrameData3[749 + lens + 16] = (byte) (sumInt6 & 0xff);
                        zhilingindex3[5]++;
                        msubVersion[3] = false;

                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "子设ID"));
                        SpiLog.print("spi13", "子设ID下  " + ByteArraysToString(mFrameData3, 749 + lens + 1, 749 + lens + 16));
                        lens += 16;
                    }
                    if (msubUpgrade[3] == true) {
                        mFrameData3[749 + lens + 1] = (byte) 0xaa;
                        mFrameData3[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData3[749 + lens + 3] = (byte) 0x31;
                        //len
                        mFrameData3[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData3[749 + lens + 5] = 0;
                        //index number
                        mFrameData3[749 + lens + 6] = (byte) (zhilingindex3[6] & 0xff);
                        mFrameData3[749 + lens + 7] = (byte) ((zhilingindex3[6] >> 8) & 0xff);
                        //mac
                        mFrameData3[749 + lens + 8] = (byte) nowsubMac3[0];
                        mFrameData3[749 + lens + 9] = (byte) nowsubMac3[1];
                        mFrameData3[749 + lens + 10] = (byte) nowsubMac3[2];
                        mFrameData3[749 + lens + 11] = (byte) nowsubMac3[3];
                        mFrameData3[749 + lens + 12] = (byte) nowsubMac3[4];
                        mFrameData3[749 + lens + 13] = (byte) nowsubMac3[5];
                        //value
                        mFrameData3[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt7 = calChecksum(mFrameData3, 749 + lens + 3, 749 + lens + 14);
                        mFrameData3[749 + lens + 15] = (byte) (sumInt7 & 0xff);

                        zhilingindex3[6]++;
                        msubUpgrade[3] = false;

                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "子机升级模式"));
                        SpiLog.print("spi13", "子升级下  " + ByteArraysToString(mFrameData3, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (msubDut[3] == true) {//settting role
                        mFrameData3[749 + lens + 1] = (byte) 0xaa;
                        mFrameData3[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData3[749 + lens + 3] = (byte) 0x32;
                        //len
                        mFrameData3[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData3[749 + lens + 5] = 0;
                        //index number
                        mFrameData3[749 + lens + 6] = (byte) (zhilingindex3[7] & 0xff);
                        mFrameData3[749 + lens + 7] = (byte) ((zhilingindex3[7] >> 8) & 0xff);
                        //value
                        mFrameData3[749 + lens + 8] = (byte) nowsubMac3[0];
                        mFrameData3[749 + lens + 9] = (byte) nowsubMac3[1];
                        mFrameData3[749 + lens + 10] = (byte) nowsubMac3[2];
                        mFrameData3[749 + lens + 11] = (byte) nowsubMac3[3];
                        mFrameData3[749 + lens + 12] = (byte) nowsubMac3[4];
                        mFrameData3[749 + lens + 13] = (byte) nowsubMac3[5];
                        //value
                        mFrameData3[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt8 = calChecksum(mFrameData3, 749 + lens + 3, 749 + lens + 14);
                        mFrameData3[749 + lens + 15] = (byte) (sumInt8 & 0xff);
                        zhilingindex3[7]++;
                        msubDut[3] = false;

                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "子设角色"));
                        SpiLog.print("spi13", "子设角色下" + ByteArraysToString(mFrameData3, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mTLclearlog[3] == true) {
                        mFrameData3[749 + lens + 1] = (byte) 0xaa;
                        mFrameData3[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData3[749 + lens + 3] = (byte) 0x4f;
                        //len
                        mFrameData3[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData3[749 + lens + 5] = 0;
                        //index number
                        mFrameData3[749 + lens + 6] = (byte) (zhilingindex3[8] & 0xff);
                        mFrameData3[749 + lens + 7] = (byte) ((zhilingindex3[8] >> 8) & 0xff);
                        //value
                        mFrameData3[749 + lens + 8] = 0x01;
                        //checksum
                        int sumInt9 = calChecksum(mFrameData3, 749 + lens + 3, 749 + lens + 8);
                        mFrameData3[749 + lens + 9] = (byte) (sumInt9 & 0xff);
                        zhilingindex3[8]++;

                        mTLclearlog[3] = false;
                        runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "清TL log"));
                        SpiLog.print("spi13", "清log下   " + ByteArraysToString(mFrameData3, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }

                }
                Arrays.fill(mReceiveData3, (byte) 0);
                mReceiveData3 = mSubG3.trySpiTransfer(mFrameData3);
                receiveData3();
                if (mFirstSend3) {
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime()));
                    mFirstSend3 = false;
                }
                if (resetfileopen3 == 1) {
                    closeSendFile();
                    openSendFile();
                    resetfileopen3 = 0;
                }
            }
        }
    }


    private void sendData4() throws Exception {
        Log.e(TAG, "sendData4");
        if (!mSendMicOrFile && mFIS4 != null) {
            int count;
            Arrays.fill(mAudioData4, (byte) 0);
            count = mFIS4.read(mAudioData4);
            if (count != 720) {
                Arrays.fill(mAudioData4, (byte) 0);
                count = 720;
                resetfileopen4 = 1;
            }
            if (true) {
                Arrays.fill(mFrameData4, (byte) 0);
                {
                    //header
                    mFrameData4[0] = (byte) 0x00;
                    mFrameData4[1] = (byte) 0x00;
                    mFrameData4[2] = (byte) 0xaa;
                    mFrameData4[3] = (byte) 0xbb;
                    //cmd
                    mFrameData4[4] = 0x01;
                    //len
                    mFrameData4[5] = (byte) (743 & 0xff);
                    mFrameData4[6] = (byte) ((743 >> 8) & 0xff);
                    //chip id //ff ff ff ff ff ff
                    if (mTstopsay) {
                        mFrameData4[7] = (byte) 0xff;
                        mFrameData4[8] = (byte) 0xff;
                        mFrameData4[9] = (byte) 0xff;
                        mFrameData4[10] = (byte) 0xff;
                        mFrameData4[11] = (byte) 0xff;
                        mFrameData4[12] = (byte) 0xff;
                    } else {
                        mFrameData4[7] = 0;
                        mFrameData4[8] = 0;
                        mFrameData4[9] = 0;
                        mFrameData4[10] = 0;
                        mFrameData4[11] = 0;
                        mFrameData4[12] = 0;
                    }
                    //device id
                    mFrameData4[13] = 0;
                    mFrameData4[14] = 0;

                    // sy info
                    mFrameData4[15] = (byte) 0x0f;//(byte) (((firsthighstatus << 4) |  firstlowstatus)&0xff);
                    mFrameData4[16] = (byte) (((secondhighstatus << 4) | secondlowstatus) & 0xff);
                    if (mTupbag) {
                        mFrameData4[17] = (byte) (((1 << 7) + (1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    } else {
                        mFrameData4[17] = (byte) (((1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    }
                    mFrameData4[18] = (byte) (fourthstatus[4] & 0xff);
                    if (fourthstatus[4] == 5) {
                        fourthstatus[4] = 0;
                    }
                    mFrameData4[19] = (byte) (fivethstatus & 0xff);
                    ;
                    mFrameData4[20] = (byte) 0x01;
                    mFrameData4[21] = (byte) 0x01;
                    mFrameData4[22] = (byte) 0x01;
                    mFrameData4[23] = (byte) 0x01;
                    mFrameData4[24] = (byte) 0x01;
                    //language
                    mFrameData4[25] = 0;
                    mFrameData4[26] = 0;
                    //index
                    mFrameData4[27] = (byte) (mTLinkIndex4 & 0xff);
                    mFrameData4[28] = (byte) ((mTLinkIndex4 >> 8) & 0xff);
                    //audio data
                    System.arraycopy(mAudioData4, 0, mFrameData4, 29, 720);
                    //sum
                    int sumInt = calChecksum(mFrameData4, 4, 748);
                    mFrameData4[749] = (byte) (sumInt & 0xff);
                    mTLinkIndex4++;
                    int lens = 0;

                    if (mStartPair[4] == true) {
                        mFrameData4[750] = (byte) 0xaa;
                        mFrameData4[751] = (byte) 0xbb;
                        //cmd
                        mFrameData4[752] = (byte) 0x10;
                        //len
                        mFrameData4[753] = (byte) (4 & 0xff);
                        mFrameData4[754] = 0;
                        //index number
                        mFrameData4[755] = (byte) (zhilingindex4[0] & 0xff);
                        mFrameData4[756] = (byte) ((zhilingindex4[0] >> 8) & 0xff);
                        //value
                        mFrameData4[757] = (byte) 0x01;
                        //checksum
                        int sumInt2 = calChecksum(mFrameData4, 752, 757);
                        mFrameData1[758] = (byte) (sumInt2 & 0xff);
                        zhilingindex4[0]++;
                        mStartPair[4] = false;
                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + " 配对"));
                        SpiLog.print("spi14", "配对下    " + ByteArraysToString(mFrameData4, 750, 758));
                        lens += 9;
                    } else if (mCancelPair[4] == true) {
                        mFrameData4[750] = (byte) 0xaa;
                        mFrameData4[751] = (byte) 0xbb;
                        //cmd
                        mFrameData4[752] = (byte) 0x10;
                        //len
                        mFrameData4[753] = (byte) (4 & 0xff);
                        mFrameData4[754] = 0;
                        //index number
                        mFrameData4[755] = (byte) (zhilingindex4[0] & 0xff);
                        mFrameData4[756] = (byte) ((zhilingindex4[0] >> 8) & 0xff);
                        //value
                        mFrameData4[757] = (byte) 0x02;
                        //checksum
                        int sumInt0 = calChecksum(mFrameData4, 752, 757);
                        mFrameData4[758] = (byte) (sumInt0 & 0xff);
                        zhilingindex4[0]++;
                        mCancelPair[4] = false;
                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "退出配对"));
                        SpiLog.print("spi14", "退出配对下" + ByteArraysToString(mFrameData4, 750, 758));
                        lens += 9;
                    }
                    if (mSendText[4] == true) {
                        mFrameData4[749 + lens + 1] = (byte) 0xaa;
                        mFrameData4[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData4[749 + lens + 3] = (byte) 0x03;
                        //len
                        mFrameData4[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData4[749 + lens + 5] = 0;
                        //index number
                        mFrameData4[749 + lens + 6] = (byte) (zhilingindex4[1] & 0xff);
                        mFrameData4[749 + lens + 7] = (byte) ((zhilingindex4[1] >> 8) & 0xff);
                        //value
                        mFrameData4[749 + lens + 8] = (byte) 0x01;
                        mFrameData4[749 + lens + 9] = (byte) 0x01;
                        mFrameData4[749 + lens + 10] = (byte) 0x01;
                        mFrameData4[749 + lens + 11] = (byte) 0x01;

                        mFrameData4[749 + lens + 12] = 0;
                        // data
                        mFrameData4[749 + lens + 13] = (byte) 0xe;
                        mFrameData4[749 + lens + 14] = (byte) 0xf;
                        //checksum

                        int sumInt1 = calChecksum(mFrameData4, 749 + lens + 3, 749 + lens + 14);
                        mFrameData4[749 + lens + 15] = (byte) (sumInt1 & 0xff);
                        zhilingindex4[1]++;
                        mSendText[4] = false;

                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "字幕"));
                        SpiLog.print("spi14", "字幕下    " + ByteArraysToString(mFrameData4, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mVersion[4] == true) {
                        mFrameData4[749 + lens + 1] = (byte) 0xaa;
                        mFrameData4[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData4[749 + lens + 3] = (byte) 0x20;
                        //len
                        mFrameData4[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData4[749 + lens + 5] = 0;
                        //index number
                        mFrameData4[749 + lens + 6] = (byte) (zhilingindex4[2] & 0xff);
                        mFrameData4[749 + lens + 7] = (byte) ((zhilingindex4[2] >> 8) & 0xff);
                        //value
                        mFrameData4[749 + lens + 8] = 0;
                        //checksum
                        int sumInt3 = calChecksum(mFrameData4, 749 + lens + 3, 749 + lens + 8);
                        mFrameData4[749 + lens + 9] = (byte) (sumInt3 & 0xff);
                        zhilingindex4[2]++;
                        mVersion[4] = false;

                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "版本"));
                        SpiLog.print("spi14", "版本下    " + ByteArraysToString(mFrameData4, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mUpgrade[4] == true) {
                        mFrameData4[749 + lens + 1] = (byte) 0xaa;
                        mFrameData4[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData4[749 + lens + 3] = (byte) 0x21;
                        //len
                        mFrameData4[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData4[749 + lens + 5] = 0;
                        //index number
                        mFrameData4[749 + lens + 6] = (byte) (zhilingindex4[3] & 0xff);
                        mFrameData4[749 + lens + 7] = (byte) ((zhilingindex4[3] >> 8) & 0xff);
                        //value
                        mFrameData4[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt4 = calChecksum(mFrameData4, 749 + lens + 3, 749 + lens + 8);
                        mFrameData4[749 + lens + 9] = (byte) (sumInt4 & 0xff);
                        zhilingindex4[3]++;
                        mUpgrade[4] = false;

                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "升级模式"));
                        SpiLog.print("spi14", "升级下    " + ByteArraysToString(mFrameData4, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mDut[4] == true) {
                        mFrameData4[749 + lens + 1] = (byte) 0xaa;
                        mFrameData4[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData4[749 + lens + 3] = (byte) 0x22;
                        //len
                        mFrameData4[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData4[749 + lens + 5] = 0;
                        //index number
                        mFrameData4[749 + lens + 6] = (byte) (zhilingindex4[4] & 0xff);
                        mFrameData4[749 + lens + 7] = (byte) ((zhilingindex4[4] >> 8) & 0xff);
                        //value
                        mFrameData4[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt5 = calChecksum(mFrameData4, 749 + lens + 3, 749 + lens + 8);
                        mFrameData4[749 + lens + 9] = (byte) (sumInt5 & 0xff);
                        zhilingindex4[4]++;
                        mDut[4] = false;

                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "DUT模式"));
                        SpiLog.print("spi14", "DUT下     " + ByteArraysToString(mFrameData4, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (msubVersion[4] == true) {//setting sub device
                        mFrameData4[749 + lens + 1] = (byte) 0xaa;
                        mFrameData4[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData4[749 + lens + 3] = (byte) 0x30;
                        //len
                        mFrameData4[749 + lens + 4] = (byte) (11 & 0xff);
                        mFrameData4[749 + lens + 5] = 0;
                        //index number
                        mFrameData4[749 + lens + 6] = (byte) (zhilingindex4[5] & 0xff);
                        mFrameData4[749 + lens + 7] = (byte) ((zhilingindex4[5] >> 8) & 0xff);
                        //mac
                        mFrameData4[749 + lens + 8] = (byte) nowsubMac4[0];
                        mFrameData4[749 + lens + 9] = (byte) nowsubMac4[1];
                        mFrameData4[749 + lens + 10] = (byte) nowsubMac4[2];
                        mFrameData4[749 + lens + 11] = (byte) nowsubMac4[3];
                        mFrameData4[749 + lens + 12] = (byte) nowsubMac4[4];
                        mFrameData4[749 + lens + 13] = (byte) nowsubMac4[5];
                        //device id
                        mFrameData4[749 + lens + 14] = 0x01;
                        mFrameData4[749 + lens + 15] = 0x02;
                        //checksum
                        int sumInt6 = calChecksum(mFrameData4, 749 + lens + 3, 749 + lens + 15);
                        mFrameData4[749 + lens + 16] = (byte) (sumInt6 & 0xff);
                        zhilingindex4[5]++;
                        msubVersion[4] = false;

                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "子设ID"));
                        SpiLog.print("spi14", "子设ID下  " + ByteArraysToString(mFrameData4, 749 + lens + 1, 749 + lens + 16));
                        lens += 16;
                    }
                    if (msubUpgrade[4] == true) {
                        mFrameData4[749 + lens + 1] = (byte) 0xaa;
                        mFrameData4[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData4[749 + lens + 3] = (byte) 0x31;
                        //len
                        mFrameData4[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData4[749 + lens + 5] = 0;
                        //index number
                        mFrameData4[749 + lens + 6] = (byte) (zhilingindex4[6] & 0xff);
                        mFrameData4[749 + lens + 7] = (byte) ((zhilingindex4[6] >> 8) & 0xff);
                        //mac
                        mFrameData4[749 + lens + 8] = (byte) nowsubMac4[0];
                        mFrameData4[749 + lens + 9] = (byte) nowsubMac4[1];
                        mFrameData4[749 + lens + 10] = (byte) nowsubMac4[2];
                        mFrameData4[749 + lens + 11] = (byte) nowsubMac4[3];
                        mFrameData4[749 + lens + 12] = (byte) nowsubMac4[4];
                        mFrameData4[749 + lens + 13] = (byte) nowsubMac4[5];
                        //value
                        mFrameData4[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt7 = calChecksum(mFrameData4, 749 + lens + 3, 749 + lens + 14);
                        mFrameData4[749 + lens + 15] = (byte) (sumInt7 & 0xff);

                        zhilingindex4[6]++;
                        msubUpgrade[4] = false;

                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "子机升级模式"));
                        SpiLog.print("spi14", "子升级下  " + ByteArraysToString(mFrameData4, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (msubDut[4] == true) {//settting role
                        mFrameData4[749 + lens + 1] = (byte) 0xaa;
                        mFrameData4[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData4[749 + lens + 3] = (byte) 0x32;
                        //len
                        mFrameData4[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData4[749 + lens + 5] = 0;
                        //index number
                        mFrameData4[749 + lens + 6] = (byte) (zhilingindex4[7] & 0xff);
                        mFrameData4[749 + lens + 7] = (byte) ((zhilingindex4[7] >> 8) & 0xff);
                        //value
                        mFrameData4[749 + lens + 8] = (byte) nowsubMac4[0];
                        mFrameData4[749 + lens + 9] = (byte) nowsubMac4[1];
                        mFrameData4[749 + lens + 10] = (byte) nowsubMac4[2];
                        mFrameData4[749 + lens + 11] = (byte) nowsubMac4[3];
                        mFrameData4[749 + lens + 12] = (byte) nowsubMac4[4];
                        mFrameData4[749 + lens + 13] = (byte) nowsubMac4[5];
                        //value
                        mFrameData4[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt8 = calChecksum(mFrameData4, 749 + lens + 3, 749 + lens + 14);
                        mFrameData4[749 + lens + 15] = (byte) (sumInt8 & 0xff);
                        zhilingindex4[7]++;
                        msubDut[4] = false;

                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "子设角色"));
                        SpiLog.print("spi14", "子设角色下" + ByteArraysToString(mFrameData4, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mTLclearlog[4] == true) {
                        mFrameData4[749 + lens + 1] = (byte) 0xaa;
                        mFrameData4[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData4[749 + lens + 3] = (byte) 0x4f;
                        //len
                        mFrameData4[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData4[749 + lens + 5] = 0;
                        //index number
                        mFrameData4[749 + lens + 6] = (byte) (zhilingindex4[8] & 0xff);
                        mFrameData4[749 + lens + 7] = (byte) ((zhilingindex4[8] >> 8) & 0xff);
                        //value
                        mFrameData4[749 + lens + 8] = 0x01;
                        //checksum
                        int sumInt9 = calChecksum(mFrameData4, 749 + lens + 3, 749 + lens + 8);
                        mFrameData4[749 + lens + 9] = (byte) (sumInt9 & 0xff);
                        zhilingindex4[8]++;

                        mTLclearlog[4] = false;
                        runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "清TL log"));
                        SpiLog.print("spi14", "清log下   " + ByteArraysToString(mFrameData4, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }

                }
                Arrays.fill(mReceiveData4, (byte) 0);
                mReceiveData4 = mSubG4.trySpiTransfer(mFrameData4);
                receiveData4();
                if (mFirstSend4) {
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime()));
                    mFirstSend4 = false;
                }
                if (resetfileopen4 == 1) {
                    closeSendFile();
                    openSendFile();
                    resetfileopen4 = 0;
                }
            }
        }
    }

    private void sendData5() throws Exception {
        Log.e(TAG, "sendData5");
        if (!mSendMicOrFile && mFIS5 != null) {
            int count;
            Arrays.fill(mAudioData5, (byte) 0);
            count = mFIS5.read(mAudioData5);
            if (count != 720) {
                Arrays.fill(mAudioData5, (byte) 0);
                count = 720;
                resetfileopen5 = 1;
            }
            if (true) {
                Arrays.fill(mFrameData5, (byte) 0);
                {
                    //header
                    mFrameData5[0] = (byte) 0x00;
                    mFrameData5[1] = (byte) 0x00;
                    mFrameData5[2] = (byte) 0xaa;
                    mFrameData5[3] = (byte) 0xbb;
                    //cmd
                    mFrameData5[4] = 0x01;
                    //len
                    mFrameData5[5] = (byte) (743 & 0xff);
                    mFrameData5[6] = (byte) ((743 >> 8) & 0xff);
                    //chip id //ff ff ff ff ff ff
                    if (mTstopsay) {
                        mFrameData5[7] = (byte) 0xff;
                        mFrameData5[8] = (byte) 0xff;
                        mFrameData5[9] = (byte) 0xff;
                        mFrameData5[10] = (byte) 0xff;
                        mFrameData5[11] = (byte) 0xff;
                        mFrameData5[12] = (byte) 0xff;
                    } else {
                        mFrameData5[7] = 0;
                        mFrameData5[8] = 0;
                        mFrameData5[9] = 0;
                        mFrameData5[10] = 0;
                        mFrameData5[11] = 0;
                        mFrameData5[12] = 0;
                    }
                    //device id
                    mFrameData5[13] = 0;
                    mFrameData5[14] = 0;

                    // sy info
                    mFrameData5[15] = (byte) 0x0f;//(byte) (((firsthighstatus << 4) |  firstlowstatus)&0xff);
                    mFrameData5[16] = (byte) (((secondhighstatus << 4) | secondlowstatus) & 0xff);
                    if (mTupbag) {
                        mFrameData5[17] = (byte) (((1 << 7) + (1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    } else {
                        mFrameData5[17] = (byte) (((1 << 6) + (getSubHeartInterval() & 0x3f)) & 0xff);
                    }
                    mFrameData5[18] = (byte) (fourthstatus[5] & 0xff);
                    if (fourthstatus[5] == 5) {
                        fourthstatus[5] = 0;
                    }
                    mFrameData5[19] = (byte) (fivethstatus & 0xff);
                    ;
                    mFrameData5[20] = (byte) 0x01;
                    mFrameData5[21] = (byte) 0x01;
                    mFrameData5[22] = (byte) 0x01;
                    mFrameData5[23] = (byte) 0x01;
                    mFrameData5[24] = (byte) 0x01;
                    //language
                    mFrameData5[25] = 0;
                    mFrameData5[26] = 0;
                    //index
                    mFrameData5[27] = (byte) (mTLinkIndex5 & 0xff);
                    mFrameData5[28] = (byte) ((mTLinkIndex5 >> 8) & 0xff);
                    //audio data
                    System.arraycopy(mAudioData5, 0, mFrameData5, 29, 720);
                    //sum
                    int sumInt = calChecksum(mFrameData5, 4, 748);
                    mFrameData5[749] = (byte) (sumInt & 0xff);
                    mTLinkIndex5++;
                    int lens = 0;

                    if (mStartPair[5] == true) {
                        mFrameData5[750] = (byte) 0xaa;
                        mFrameData5[751] = (byte) 0xbb;
                        //cmd
                        mFrameData5[752] = (byte) 0x10;
                        //len
                        mFrameData5[753] = (byte) (4 & 0xff);
                        mFrameData5[754] = 0;
                        //index number
                        mFrameData5[755] = (byte) (zhilingindex5[0] & 0xff);
                        mFrameData5[756] = (byte) ((zhilingindex5[0] >> 8) & 0xff);
                        //value
                        mFrameData5[757] = (byte) 0x01;
                        //checksum
                        int sumInt2 = calChecksum(mFrameData5, 752, 757);
                        mFrameData5[758] = (byte) (sumInt2 & 0xff);
                        zhilingindex5[0]++;
                        mStartPair[5] = false;
                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + " 配对"));
                        SpiLog.print("spi15", "配对下    " + ByteArraysToString(mFrameData5, 750, 758));
                        lens += 9;
                    } else if (mCancelPair[5] == true) {
                        mFrameData5[750] = (byte) 0xaa;
                        mFrameData5[751] = (byte) 0xbb;
                        //cmd
                        mFrameData5[752] = (byte) 0x10;
                        //len
                        mFrameData5[753] = (byte) (4 & 0xff);
                        mFrameData5[754] = 0;
                        //index number
                        mFrameData5[755] = (byte) (zhilingindex5[0] & 0xff);
                        mFrameData5[756] = (byte) ((zhilingindex5[0] >> 8) & 0xff);
                        //value
                        mFrameData5[757] = (byte) 0x02;
                        //checksum
                        int sumInt0 = calChecksum(mFrameData5, 752, 757);
                        mFrameData5[758] = (byte) (sumInt0 & 0xff);
                        zhilingindex5[0]++;
                        mCancelPair[5] = false;
                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "退出配对"));
                        SpiLog.print("spi15", "退出配对下" + ByteArraysToString(mFrameData5, 750, 758));
                        lens += 9;
                    }
                    if (mSendText[5] == true) {
                        mFrameData5[749 + lens + 1] = (byte) 0xaa;
                        mFrameData5[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData5[749 + lens + 3] = (byte) 0x03;
                        //len
                        mFrameData5[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData5[749 + lens + 5] = 0;
                        //index number
                        mFrameData5[749 + lens + 6] = (byte) (zhilingindex5[1] & 0xff);
                        mFrameData5[749 + lens + 7] = (byte) ((zhilingindex5[1] >> 8) & 0xff);
                        //value
                        mFrameData5[749 + lens + 8] = (byte) 0x01;
                        mFrameData5[749 + lens + 9] = (byte) 0x01;
                        mFrameData5[749 + lens + 10] = (byte) 0x01;
                        mFrameData5[749 + lens + 11] = (byte) 0x01;

                        mFrameData5[749 + lens + 12] = 0;
                        // data
                        mFrameData5[749 + lens + 13] = (byte) 0xe;
                        mFrameData5[749 + lens + 14] = (byte) 0xf;
                        //checksum

                        int sumInt1 = calChecksum(mFrameData5, 749 + lens + 3, 749 + lens + 14);
                        mFrameData5[749 + lens + 15] = (byte) (sumInt1 & 0xff);
                        zhilingindex5[1]++;
                        mSendText[5] = false;

                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "字幕"));
                        SpiLog.print("spi15", "字幕下    " + ByteArraysToString(mFrameData5, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mVersion[5] == true) {
                        mFrameData5[749 + lens + 1] = (byte) 0xaa;
                        mFrameData5[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData5[749 + lens + 3] = (byte) 0x20;
                        //len
                        mFrameData5[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData5[749 + lens + 5] = 0;
                        //index number
                        mFrameData5[749 + lens + 6] = (byte) (zhilingindex5[2] & 0xff);
                        mFrameData5[749 + lens + 7] = (byte) ((zhilingindex5[2] >> 8) & 0xff);
                        //value
                        mFrameData5[749 + lens + 8] = 0;
                        //checksum
                        int sumInt3 = calChecksum(mFrameData5, 749 + lens + 3, 749 + lens + 8);
                        mFrameData5[749 + lens + 9] = (byte) (sumInt3 & 0xff);
                        zhilingindex5[2]++;
                        mVersion[5] = false;

                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "版本"));
                        SpiLog.print("spi15", "版本下    " + ByteArraysToString(mFrameData5, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mUpgrade[5] == true) {
                        mFrameData5[749 + lens + 1] = (byte) 0xaa;
                        mFrameData5[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData5[749 + lens + 3] = (byte) 0x21;
                        //len
                        mFrameData5[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData5[749 + lens + 5] = 0;
                        //index number
                        mFrameData5[749 + lens + 6] = (byte) (zhilingindex5[3] & 0xff);
                        mFrameData5[749 + lens + 7] = (byte) ((zhilingindex5[3] >> 8) & 0xff);
                        //value
                        mFrameData5[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt4 = calChecksum(mFrameData5, 749 + lens + 3, 749 + lens + 8);
                        mFrameData5[749 + lens + 9] = (byte) (sumInt4 & 0xff);
                        zhilingindex5[3]++;
                        mUpgrade[5] = false;

                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "升级模式"));
                        SpiLog.print("spi15", "升级下    " + ByteArraysToString(mFrameData5, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (mDut[5] == true) {
                        mFrameData5[749 + lens + 1] = (byte) 0xaa;
                        mFrameData5[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData5[749 + lens + 3] = (byte) 0x22;
                        //len
                        mFrameData5[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData5[749 + lens + 5] = 0;
                        //index number
                        mFrameData5[749 + lens + 6] = (byte) (zhilingindex5[4] & 0xff);
                        mFrameData5[749 + lens + 7] = (byte) ((zhilingindex5[4] >> 8) & 0xff);
                        //value
                        mFrameData5[749 + lens + 8] = (byte) 0x01;
                        //checksum
                        int sumInt5 = calChecksum(mFrameData5, 749 + lens + 3, 749 + lens + 8);
                        mFrameData5[749 + lens + 9] = (byte) (sumInt5 & 0xff);
                        zhilingindex5[4]++;
                        mDut[5] = false;

                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "DUT模式"));
                        SpiLog.print("spi15", "DUT下     " + ByteArraysToString(mFrameData5, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                    if (msubVersion[5] == true) {//setting sub device
                        mFrameData5[749 + lens + 1] = (byte) 0xaa;
                        mFrameData5[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData5[749 + lens + 3] = (byte) 0x30;
                        //len
                        mFrameData5[749 + lens + 4] = (byte) (11 & 0xff);
                        mFrameData5[749 + lens + 5] = 0;
                        //index number
                        mFrameData5[749 + lens + 6] = (byte) (zhilingindex5[5] & 0xff);
                        mFrameData5[749 + lens + 7] = (byte) ((zhilingindex5[5] >> 8) & 0xff);
                        //mac
                        mFrameData5[749 + lens + 8] = (byte) nowsubMac5[0];
                        mFrameData5[749 + lens + 9] = (byte) nowsubMac5[1];
                        mFrameData5[749 + lens + 10] = (byte) nowsubMac5[2];
                        mFrameData5[749 + lens + 11] = (byte) nowsubMac5[3];
                        mFrameData5[749 + lens + 12] = (byte) nowsubMac5[4];
                        mFrameData5[749 + lens + 13] = (byte) nowsubMac5[5];
                        //device id
                        mFrameData5[749 + lens + 14] = 0x01;
                        mFrameData5[749 + lens + 15] = 0x02;
                        //checksum
                        int sumInt6 = calChecksum(mFrameData5, 749 + lens + 3, 749 + lens + 15);
                        mFrameData5[749 + lens + 16] = (byte) (sumInt6 & 0xff);
                        zhilingindex5[5]++;
                        msubVersion[5] = false;

                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "子设ID"));
                        SpiLog.print("spi15", "子设ID下  " + ByteArraysToString(mFrameData5, 749 + lens + 1, 749 + lens + 16));
                        lens += 16;
                    }
                    if (msubUpgrade[5] == true) {
                        mFrameData5[749 + lens + 1] = (byte) 0xaa;
                        mFrameData5[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData5[749 + lens + 3] = (byte) 0x31;
                        //len
                        mFrameData5[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData5[749 + lens + 5] = 0;
                        //index number
                        mFrameData5[749 + lens + 6] = (byte) (zhilingindex5[6] & 0xff);
                        mFrameData5[749 + lens + 7] = (byte) ((zhilingindex5[6] >> 8) & 0xff);
                        //mac
                        mFrameData5[749 + lens + 8] = (byte) nowsubMac5[0];
                        mFrameData5[749 + lens + 9] = (byte) nowsubMac5[1];
                        mFrameData5[749 + lens + 10] = (byte) nowsubMac5[2];
                        mFrameData5[749 + lens + 11] = (byte) nowsubMac5[3];
                        mFrameData5[749 + lens + 12] = (byte) nowsubMac5[4];
                        mFrameData5[749 + lens + 13] = (byte) nowsubMac5[5];
                        //value
                        mFrameData5[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt7 = calChecksum(mFrameData5, 749 + lens + 3, 749 + lens + 14);
                        mFrameData5[749 + lens + 15] = (byte) (sumInt7 & 0xff);

                        zhilingindex5[6]++;
                        msubUpgrade[5] = false;

                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "子机升级模式"));
                        SpiLog.print("spi15", "子升级下  " + ByteArraysToString(mFrameData5, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (msubDut[5] == true) {//settting role
                        mFrameData5[749 + lens + 1] = (byte) 0xaa;
                        mFrameData5[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData5[749 + lens + 3] = (byte) 0x32;
                        //len
                        mFrameData5[749 + lens + 4] = (byte) (10 & 0xff);
                        mFrameData5[749 + lens + 5] = 0;
                        //index number
                        mFrameData5[749 + lens + 6] = (byte) (zhilingindex5[7] & 0xff);
                        mFrameData5[749 + lens + 7] = (byte) ((zhilingindex5[7] >> 8) & 0xff);
                        //value
                        mFrameData5[749 + lens + 8] = (byte) nowsubMac5[0];
                        mFrameData5[749 + lens + 9] = (byte) nowsubMac5[1];
                        mFrameData5[749 + lens + 10] = (byte) nowsubMac5[2];
                        mFrameData5[749 + lens + 11] = (byte) nowsubMac5[3];
                        mFrameData5[749 + lens + 12] = (byte) nowsubMac5[4];
                        mFrameData5[749 + lens + 13] = (byte) nowsubMac5[5];
                        //value
                        mFrameData5[749 + lens + 14] = 0x01;
                        //checksum
                        int sumInt8 = calChecksum(mFrameData5, 749 + lens + 3, 749 + lens + 14);
                        mFrameData5[749 + lens + 15] = (byte) (sumInt8 & 0xff);
                        zhilingindex5[7]++;
                        msubDut[5] = false;

                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "子设角色"));
                        SpiLog.print("spi15", "子设角色下" + ByteArraysToString(mFrameData5, 749 + lens + 1, 749 + lens + 15));
                        lens += 15;
                    }
                    if (mTLclearlog[5] == true) {
                        mFrameData5[749 + lens + 1] = (byte) 0xaa;
                        mFrameData5[749 + lens + 2] = (byte) 0xbb;
                        //cmd
                        mFrameData5[749 + lens + 3] = (byte) 0x4f;
                        //len
                        mFrameData5[749 + lens + 4] = (byte) (4 & 0xff);
                        mFrameData5[749 + lens + 5] = 0;
                        //index number
                        mFrameData5[749 + lens + 6] = (byte) (zhilingindex5[8] & 0xff);
                        mFrameData5[749 + lens + 7] = (byte) ((zhilingindex5[8] >> 8) & 0xff);
                        //value
                        mFrameData5[749 + lens + 8] = 0x01;
                        //checksum
                        int sumInt9 = calChecksum(mFrameData5, 749 + lens + 3, 749 + lens + 8);
                        mFrameData5[749 + lens + 9] = (byte) (sumInt9 & 0xff);
                        zhilingindex5[8]++;
                        mTLclearlog[5] = false;
                        runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "清TL log"));
                        SpiLog.print("spi15", "清log下   " + ByteArraysToString(mFrameData5, 749 + lens + 1, 749 + lens + 9));
                        lens += 9;
                    }
                }
                Arrays.fill(mReceiveData5, (byte) 0);
                mReceiveData5 = mSubG5.trySpiTransfer(mFrameData5);
                receiveData5();
                if (mFirstSend5) {
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime()));
                    mFirstSend5 = false;
                }
                if (resetfileopen5 == 1) {
                    closeSendFile();
                    openSendFile();
                    resetfileopen5 = 0;
                }
            }
        }
    }


    private void openSendFile() {
        try {
            if (checkBox.isChecked()) {
                mFIS = new FileInputStream(Environment.getExternalStorageDirectory() + File.separator + "spi.pcm");
            }
            if (checkBox1.isChecked()) {
                mFIS1 = new FileInputStream(Environment.getExternalStorageDirectory() + File.separator + "spi1.pcm");
            }
            if (checkBox2.isChecked()) {
                mFIS2 = new FileInputStream(Environment.getExternalStorageDirectory() + File.separator + "spi2.pcm");
            }
            if (checkBox3.isChecked()) {
                mFIS3 = new FileInputStream(Environment.getExternalStorageDirectory() + File.separator + "spi3.pcm");
            }
            if (checkBox4.isChecked()) {
                mFIS4 = new FileInputStream(Environment.getExternalStorageDirectory() + File.separator + "spi4.pcm");
            }
            if (checkBox5.isChecked()) {
                mFIS5 = new FileInputStream(Environment.getExternalStorageDirectory() + File.separator + "spi5.pcm");
            }

        } catch (Exception e) {
            Toast.makeText(getApplicationContext(), "send pcm file is not exist", Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }
    }

    private void closeSendFile() {
        try {
            if (mFIS != null) {
                mFIS.close();
                mFIS = null;
            }
            if (mFIS1 != null) {
                mFIS1.close();
                mFIS1 = null;
            }
            if (mFIS2 != null) {
                mFIS2.close();
                mFIS2 = null;
            }
            if (mFIS3 != null) {
                mFIS3.close();
                mFIS3 = null;
            }
            if (mFIS4 != null) {
                mFIS4.close();
                mFIS4 = null;
            }
            if (mFIS5 != null) {
                mFIS5.close();
                mFIS5 = null;
            }
        } catch (Exception e) {
            Toast.makeText(getApplicationContext(), "close send pcm file is failed", Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }
    }

    /**
     * private void receiveData() throws Exception {
     * int checkbeginmic = 0;
     * for(int k= 0 ; k< 30; k++){
     * if(mReceiveData[20+k] != 0){
     * checkbeginmic = 1;
     * }
     * }
     * if(removefirstnumber < 11 ){
     * removefirstnumber++;
     * }
     * if(checkbeginmic == 1){
     * mStreamAudioTrack.write(mReceiveData, 19, 720);
     * }
     * int checkindexslow = mReceiveData[17] & 0xFF;
     * int checkindexhigh = mReceiveData[18] & 0xFF;
     * int index = checkindexslow + checkindexhigh*256;
     * Log.i(TAG, "index = " + index + "  receiveData2 teric count = " + mReceiveData.length + ", " + Arrays.toString(mReceiveData));
     * if(removefirstnumber >= 11){
     * totalsendnumber++;
     * }
     * Log.i(TAG, "removefirstnumber = " + removefirstnumber + "  totalsendnumber = " +totalsendnumber);
     * if(index != 0 && (index - lasttlinkIndex) >= 2 ){
     * Log.i(TAG, "receiveData checkindexslow = " + removefirstnumber + "checkindexhigh = " + totalsendnumber + " index= "+index + "lastindex = " + lasttlinkIndex);
     * //runOnUiThread(() -> addText(mTextSend, "loss one"));
     * if(removefirstnumber >= 11) {
     * losstotalnumber += index - lasttlinkIndex;
     * }
     * }
     * if((lasttlinkIndex > index) && ((65536 - lasttlinkIndex >= 2) || (65536 - lasttlinkIndex == 1 && index >= 1))) {
     * Log.i(TAG, "receiveData checkindexslow2 = " + removefirstnumber + "checkindexhigh = " + totalsendnumber + " index= " + index + "lastindex = " + lasttlinkIndex);
     * //runOnUiThread(() -> addText(mTextSend, "loss one"));
     * if(removefirstnumber >= 11) {
     * if ((65536 - lasttlinkIndex >= 2)) {
     * losstotalnumber += 65536 - lasttlinkIndex;
     * } else {
     * losstotalnumber += index;
     * }
     * }
     * }
     * lasttlinkIndex = index;
     * totallinkIndex++;
     * if(removefirstnumber >= 11) {
     * String tempstr = "接收包总数=" + String.valueOf(totalsendnumber) + "丢包数=" + String.valueOf(losstotalnumber);
     * mTextloss.setText(tempstr);
     * }
     * <p>
     * if(mTReceivermicbegin != checkbeginmic && checkbeginmic == 1) {
     * runOnUiThread(() -> addText(mTextSend, " mic begin"+formatDataTime()));
     * }else if(mTReceivermicbegin != checkbeginmic && checkbeginmic == 0) {
     * runOnUiThread(() -> addText(mTextSend, " mic end"+formatDataTime()));
     * }
     * mTReceivermicbegin = checkbeginmic;
     * if(mFOS != null) {
     * mFOS.write(mReceiveData, 19, 720);
     * }
     * }
     */


    public void parseMessage() throws Exception {
        Log.i(TAG, "parseMessage = 0");
        byte onebyte = mReceiveData[0];
        byte twobyte = mReceiveData[1];
        byte threebyte = mReceiveData[2];
        byte fourbyte = mReceiveData[3];
        byte cmdbyte = mReceiveData[4];
        int currentindex = 0;
        Log.i(TAG, "parseMessage = 1");
        while ((threebyte & 0xFF) == 0xaa && (fourbyte & 0xFF) == 0xcc) {
            switch (cmdbyte & 0xFF) {
                case 0x01:
                    //audio
                {

                    int msglenSize = (mReceiveData[6] & 0xFF) * 256 + (mReceiveData[5] & 0xFF);
                    Log.i(TAG, "parseMessage = msglenSize " + msglenSize);
                    int sum = 0;
                    for (int i = 4; i <= (msglenSize + 5); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[msglenSize + 6] & 0xFF)) {
                        Log.i(TAG, "parseMessage = 3 fail =" + (sum & 0xFF));
                    }
                    currentindex += msglenSize + 7;
                    byte[] macid = new byte[6];
                    System.arraycopy(mReceiveData, 7, macid, 0, 6);
                    int deviceid = ((mReceiveData[14] & 0xFF) * 256) + (mReceiveData[13] & 0xFF);
                    byte[] syninfo = new byte[10];
                    System.arraycopy(mReceiveData, 15, syninfo, 0, 10);
                    int laninfo = (mReceiveData[25] & 0xFF);
                    int speechinfo = (mReceiveData[26] & 0xFF);
                    int soundtotalsize = (mReceiveData[27] & 0xFF) + ((mReceiveData[28] & 0xFF) * 256);
                    int checkbeginmic = 0;
                    for (int k = 0; k < 30; k++) {
                        if (mReceiveData[50 + k] != 0) {
                            checkbeginmic = 1;
                        }
                    }
                    if (checkbeginmic == 1) {
                        if (whichmicbegin != 0) {
                            //runOnUiThread(() -> addText(mTextReceive0, "spi0抢麦"));
                            whichmicbegin = 0;
                        }
                        if (whichmicbegin == 0)
                            mStreamAudioTrack.write(mReceiveData, 29, 720);
                    }
                    int checkindexslow = (mReceiveData[27] & 0xFF);
                    int checkindexhigh = (mReceiveData[28] & 0xFF);
                    int recnowindex = checkindexslow + checkindexhigh * 256;

                    Log.i(TAG, "recnowindex = " + recnowindex + "  receiveData teric count = " + mReceiveData.length + ", " + ByteArraysToString(mReceiveData, 0, 850));
                    if (checkbeginmic == 1 && mFOS != null) {
                        mFOS.write(mReceiveData, 29, 720);
                    }
                    if (mTLinkspiuptotal > 1) {
                        if (mTLinkspiuplastIndex != 0 && (recnowindex - mTLinkspiuplastIndex) >= 2) {
                            mTLinkspiuplosstotal++;
                        }
                        if ((mTLinkspiuplastIndex > recnowindex) && ((65536 - mTLinkspiuplastIndex >= 2) || (65536 - mTLinkspiuplastIndex == 1 && recnowindex >= 1))) {
                            if ((65536 - mTLinkspiuplastIndex >= 2)) {
                                mTLinkspiuplosstotal++;
                            } else {
                                mTLinkspiuplosstotal++;
                            }
                        }
                    }
                    mTLinkspiuplastIndex = recnowindex;
                    mTLinkspiuptotal++;
                    Log.i(TAG, "mTLinkspiuplosstotal = " + mTLinkspiuplosstotal + "recnowindex=" + recnowindex + " teric mTLinkspiuplastIndex = " + mTLinkspiuplastIndex + ", mTLinkspiuptotal =" + mTLinkspiuptotal);

                }
                //if(checkbeginmic == 1 && mFOS != null) {
                //    mFOS.write(mReceiveData, 29, 720);
                //}
                break;
                case 0x02:
                    //qiang mic total 18 =13
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "qiang mic index"));
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        break;
                    }
                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex(indextotalsize,0);
                    byte[] macid = new byte[9];
                    System.arraycopy(mReceiveData, currentindex + 7, macid, 0, 6);
                    macid[6] = mReceiveData[currentindex + 5];
                    macid[7] = mReceiveData[currentindex + 6];
                    macid[8] = 0;
                    int storagesize = gstorage1.size();
                    int nowindex = gstorage1.addOrGetIndex(macid);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal[0]++;
                    } else {
                        byte[] arr = gstorage1.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x0) {
                            int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                            if (indextotalsize != 1) {
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal += indextotalsize;
                                }
                            }
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal[0]++;
                        } else {
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal[0]++;
                        }
                    }
                    int deviceid = ((mReceiveData[currentindex + 14] & 0xFF) * 256) + (mReceiveData[currentindex + 13] & 0xFF);
                    int highbit = ((mReceiveData[currentindex + 15] >> 4) & 0xF);
                    int lowbit = (mReceiveData[currentindex + 15] & 0xF);

                    int highchannelbit = ((mReceiveData[currentindex + 16] >> 4) & 0xF);
                    int lowrolebit = (mReceiveData[currentindex + 16] & 0xF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "上行抢麦" + tempstringoff));
                    SpiLog.print("spi10", "上行抢麦  " + ByteArraysToString(mReceiveData, currentindex, currentindex + 17));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x10:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "pair index"));
                    Log.i(TAG, "parseMessage currentindex =" + currentindex);
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData[currentindex + 7] & 0xFF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "配对" + tempstringoff));
                    SpiLog.print("spi10", "配对      " + ByteArraysToString(mReceiveData, currentindex, currentindex + 8));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x11:
                    //pair sub info
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "pair sub info index"));
                    int msglenSize = (mReceiveData[currentindex + 4] & 0xFF) * 256 + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);
                    checkTLinkUpIndex(indextotalsize, 1);
                    byte[] submacid = new byte[6];
                    System.arraycopy(mReceiveData, currentindex + 7, submacid, 0, 6);
                    System.arraycopy(mReceiveData, currentindex + 7, nowsubMac, 0, 6);
                    int deviceid = ((mReceiveData[currentindex + 14] & 0xFF) * 256) + (mReceiveData[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData[currentindex + 16] & 0xFF);
                    // revert 32 byte
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "配对上报" + tempstringoff));
                    SpiLog.print("spi10", "配对上报  " + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x12:
                    //sub heart packag
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "sub heart packag index"));
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "sub heart packag checksum fail " + (sum & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex(indextotalsize,2);
                    byte[] submacid = new byte[9];
                    System.arraycopy(mReceiveData, currentindex + 7, submacid, 0, 6);
                    submacid[6] = mReceiveData[currentindex + 5];
                    submacid[7] = mReceiveData[currentindex + 6];
                    submacid[8] = 0;
                    int storagesize = gstorage.size();
                    int nowindex = gstorage.addOrGetIndex(submacid);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal[2]++;
                    } else {
                        byte[] arr = gstorage.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x0) {
                            int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                            if (indextotalsize != 1) {
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal += indextotalsize;
                                }
                            }
                            gstorage.getStorage().set(nowindex, submacid);
                            mTLinkindexuptotal[2]++;
                        } else {
                            gstorage.getStorage().set(nowindex, submacid);
                            mTLinkindexuptotal[2]++;
                        }
                    }
                    System.arraycopy(mReceiveData, currentindex + 7, nowsubMac, 0, 6);
                    int deviceid = ((mReceiveData[currentindex + 14] & 0xFF) * 256) + (mReceiveData[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData[currentindex + 16] & 0xFF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "子机心跳" + tempstringoff));
                    SpiLog.print("spi10", "子机心跳  " + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));
                    // revert 32 byte
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x20:
                    //tx tlink chip sw version
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "tx tlink chip sw version index"));
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);

                    int versioninfo = (mReceiveData[currentindex + 7] & 0xFF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "版本" + tempstringoff));
                    SpiLog.print("spi10", "版本      " + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x21:
                    //tx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "tx tlink chip upgrade index"));
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);

                    int upgrademode = (mReceiveData[currentindex + 7] & 0xFF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "升级" + tempstringoff));
                    SpiLog.print("spi10", "升级      " + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x22:
                    //tx tlink chip enter/exit dut mode
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "tx tlink chip dut index"));
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);

                    int dutmode = (mReceiveData[currentindex + 7] & 0xFF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "Dut" + tempstringoff));
                    SpiLog.print("spi10", "Dut       " + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x30:
                    //sub rx tlink chip setting device number
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "sub rx tlink chip setting device number index"));
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid = new byte[6];
                    System.arraycopy(mReceiveData, currentindex + 7, submacid, 0, 6);
                    int deviceid = ((mReceiveData[currentindex + 14] & 0xFF) * 256) + (mReceiveData[currentindex + 13] & 0xFF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 14);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "设备号" + tempstringoff));
                    SpiLog.print("spi10", "设备号    " + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;


                }
                break;
                case 0x31:
                    //sub rx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "sub rx tlink chip upgrade mode index"));
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid = new byte[6];
                    System.arraycopy(mReceiveData, currentindex + 7, submacid, 0, 6);
                    int dutmode = (mReceiveData[currentindex + 13] & 0xFF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "子升级" + tempstringoff));
                    SpiLog.print("spi10", "子升级    " + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }

                break;
                case 0x32:
                    //sub rx tlink chip setting rode
                {
                    Log.i(TAG, "parseMessage = 32");
                    //runOnUiThread(() -> addText(mTextReceive0, "sub rx tlink chip setting rode  index"));
                    int msglenSize = (mReceiveData[currentindex + 4] & 0xFF) * 256 + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage sum=" + (sum & 0xFF) + "=" + (currentindex + msglenSize + 4) + "=" + (mReceiveData[currentindex + msglenSize + 4] & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid = new byte[6];
                    System.arraycopy(mReceiveData, currentindex + 7, submacid, 0, 6);
                    int mrole = (mReceiveData[currentindex + 13] & 0xFF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "子角色" + tempstringoff));
                    SpiLog.print("spi10", "子角色    " + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));

                    currentindex += msglenSize + 5;
                }
                break;
                case 0x40:
                    //tx tlink send log infos
                {
                    Log.i(TAG, "parseMessage = 0x40");
                    //runOnUiThread(() -> addText(mTextReceive0, formatDataTime()+"tx log infos "));
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage = 0x40 sum=" + (sum & 0xFF));
                        return;
                    }
                    Log.i(TAG, "parseMessage = 0x40 2");
                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);

                    int temprftotal = (mReceiveData[currentindex + 7] & 0xFF) + ((mReceiveData[currentindex + 8] & 0xFF) << 8) + ((mReceiveData[currentindex + 9] & 0xFF) << 16) + ((mReceiveData[currentindex + 10] & 0xFF) << 24);
                    int temprfloss = (mReceiveData[currentindex + 11] & 0xFF) + ((mReceiveData[currentindex + 12] & 0xFF) << 8) + ((mReceiveData[currentindex + 13] & 0xFF) << 16) + ((mReceiveData[currentindex + 14] & 0xFF) << 24);
                    if (temprftotal != 0) {
                        mTLinkRFIndex = temprftotal;
                        mTLinkRFlosstotal = temprfloss;
                    }
                    //Log.i(TAG, "="+(mReceiveData[currentindex+7]& 0xFF) + "="+(mReceiveData[currentindex+8]& 0xFF) + "="+(mReceiveData[currentindex+9]& 0xFF)+ "="+(mReceiveData[currentindex+10]& 0xFF));
                    //Log.i(TAG, "="+(mReceiveData[currentindex+11]& 0xFF) + "="+(mReceiveData[currentindex+12]& 0xFF) + "="+(mReceiveData[currentindex+13]& 0xFF)+ "="+(mReceiveData[currentindex+14]& 0xFF));
                    mTLinkspidownlosstotal = (mReceiveData[currentindex + 15] & 0xFF) + ((mReceiveData[currentindex + 16] & 0xFF) * 256);
                    mTLinkindexdownlosstotal = (mReceiveData[currentindex + 17] & 0xFF) + ((mReceiveData[currentindex + 18] & 0xFF) * 256);
                    localrfrssi = (mReceiveData[currentindex + 19] & 0xFF);
                    romoterfrssi = (mReceiveData[currentindex + 20] & 0xFF);
                    Log.i(TAG, "parseMessage mTLinkRFIndex=" + mTLinkRFIndex + "=mTLinkRFlosstotal" + mTLinkRFlosstotal + "=" + mTLinkspidownlosstotal + "=" + mTLinkindexdownlosstotal + "=" + localrfrssi);
                    //revert 10 byte
                    SpiLog.print("spi0", "Log:" + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));
                    int totalindex = 0;
                    for (int k = 0; k < 9; k++) {
                        totalindex = totalindex + (zhilingindex[k] - 1);
                    }
                    int totalupindex = 0;
                    for (int n = 0; n < 3; n++) {
                        totalupindex = totalupindex + mTLinkindexuptotal[n];
                    }

                    //NstartSaveLog("Log:"+ByteArraysToString(mReceiveData,currentindex,currentindex+msglenSize+4),"spi0",mTLinkRFIndex,mTLinkRFlosstotal,mTLinkIndex-1,mTLinkspidownlosstotal,mTLinkspiuptotal,mTLinkspiuplosstotal,zhilingindex-1,mTLinkindexdownlosstotal-1,mTLinkindexuptotal,mTLinkindexuplosstotal,localrfrssi,romoterfrssi);
                    currentindex += msglenSize + 5;
                    for (int j = 0; j < deviceList.size(); j++) {
                        if (deviceList.get(j).getName().equals("spi0")) {
                            deviceList.set(j, new SpiDeviceModel("spi0", mTLinkRFIndex, mTLinkRFlosstotal, mTLinkIndex - 1, mTLinkspidownlosstotal, mTLinkspiuptotal, mTLinkspiuplosstotal, totalindex, mTLinkindexdownlosstotal, totalupindex, mTLinkindexuplosstotal, localrfrssi, romoterfrssi));
                            Log.i(TAG, "parseMessage = 0x40 3");
                        }
                    }
                    //NstartSaveLog("spi0",mTLinkRFIndex,mTLinkRFlosstotal,mTLinkIndex-1,mTLinkspidownlosstotal,mTLinkspiuptotal,mTLinkspiuplosstotal,zhilingindex-1,mTLinkindexdownlosstotal-1,mTLinkindexuptotal,mTLinkindexuplosstotal,localrfrssi,romoterfrssi);

                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            adapter.notifyDataSetChanged();
                        }
                    });
                }
                break;
                case 0x4f:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive0, "pair index"));
                    int msglenSize = ((mReceiveData[currentindex + 4] & 0xFF) * 256) + (mReceiveData[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData[currentindex + 5] & 0xFF) + ((mReceiveData[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData[currentindex + 7] & 0xFF);
                    tempstringoff = ByteArraysToString(mReceiveData, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive0, formatDataTime() + "清log" + tempstringoff));
                    SpiLog.print("spi10", "清log     " + ByteArraysToString(mReceiveData, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                    //runOnUiThread(() -> addText(mTextReceive0, formatDataTime()+"0清除发射log统计"+currentstatus));
                }
                break;
                default:
                    return;
            }
            if (mReceiveData.length > (currentindex + 2) && mReceiveData[currentindex] != 0) {
                threebyte = mReceiveData[currentindex];
                fourbyte = mReceiveData[currentindex + 1];
                cmdbyte = mReceiveData[currentindex + 2];
                Log.i(TAG, "parseMessage = 55=" + mReceiveData[currentindex]);
            } else {
                threebyte = 0;
                fourbyte = 0;
                cmdbyte = 0;
                Log.i(TAG, "parseMessage = 66");
            }
            Log.i(TAG, "parseMessage = 77");
        }
    }

    public void parseMessage1() throws Exception {
        Log.i(TAG, "parseMessage1 = 0");
        byte onebyte = mReceiveData1[0];
        byte twobyte = mReceiveData1[1];
        byte threebyte = mReceiveData1[2];
        byte fourbyte = mReceiveData1[3];
        byte cmdbyte = mReceiveData1[4];
        int currentindex = 0;
        Log.i(TAG, "parseMessage1 = 1");
        while ((threebyte & 0xFF) == 0xaa && (fourbyte & 0xFF) == 0xcc) {
            switch (cmdbyte & 0xFF) {
                case 0x01:
                    //audio
                {

                    int msglenSize = (mReceiveData1[6] & 0xFF) * 256 + (mReceiveData1[5] & 0xFF);
                    Log.i(TAG, "parseMessage1 = msglenSize " + msglenSize);
                    int sum = 0;
                    for (int i = 4; i <= (msglenSize + 5); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[msglenSize + 6] & 0xFF)) {
                        Log.i(TAG, "parseMessage1 = 3 fail =" + (sum & 0xFF));
                    }
                    currentindex += msglenSize + 7;
                    byte[] macid = new byte[6];
                    System.arraycopy(mReceiveData1, 7, macid, 0, 6);
                    int deviceid = ((mReceiveData1[14] & 0xFF) * 256) + (mReceiveData1[13] & 0xFF);
                    byte[] syninfo = new byte[10];
                    System.arraycopy(mReceiveData1, 15, syninfo, 0, 10);
                    int laninfo = (mReceiveData1[25] & 0xFF);
                    int speechinfo = (mReceiveData1[26] & 0xFF);
                    int soundtotalsize = (mReceiveData1[27] & 0xFF) + ((mReceiveData1[28] & 0xFF) * 256);
                    int checkbeginmic = 0;
                    for (int k = 0; k < 30; k++) {
                        if (mReceiveData1[50 + k] != 0) {
                            checkbeginmic = 1;
                        }
                    }
                    if (checkbeginmic == 1) {
                        if (whichmicbegin != 1) {
                            //runOnUiThread(() -> addText(mTextReceive1, formatDataTime()+" spi1抢麦"));
                            whichmicbegin = 1;
                        }
                        if (whichmicbegin == 1)
                            mStreamAudioTrack1.write(mReceiveData1, 29, 720);
                    }
                    int checkindexslow = (mReceiveData1[27] & 0xFF);
                    int checkindexhigh = (mReceiveData1[28] & 0xFF);
                    int recnowindex = checkindexslow + checkindexhigh * 256;

                    Log.i(TAG, "recnowindex = " + recnowindex + "  receiveData teric count = " + mReceiveData1.length + ", " + ByteArraysToString(mReceiveData1, 0, 850));
                    if (checkbeginmic == 1 && mFOS1 != null) {
                        mFOS1.write(mReceiveData1, 29, 720);
                    }
                    if (mTLinkspiuptotal1 > 1) {
                        if (mTLinkspiuplastIndex1 != 0 && (recnowindex - mTLinkspiuplastIndex1) >= 2) {
                            mTLinkspiuplosstotal1++;
                        }
                        if ((mTLinkspiuplastIndex1 > recnowindex) && ((65536 - mTLinkspiuplastIndex1 >= 2) || (65536 - mTLinkspiuplastIndex1 == 1 && recnowindex >= 1))) {
                            if ((65536 - mTLinkspiuplastIndex1 >= 2)) {
                                mTLinkspiuplosstotal1++;
                            } else {
                                mTLinkspiuplosstotal1++;
                            }
                        }
                    }
                    mTLinkspiuplastIndex1 = recnowindex;
                    mTLinkspiuptotal1++;
                    Log.i(TAG, "mTLinkspiuplosstotal1 = " + mTLinkspiuplosstotal1 + "recnowindex1=" + recnowindex + " teric mTLinkspiuplastIndex1 = " + mTLinkspiuplastIndex1 + ", mTLinkspiuptotal1 =" + mTLinkspiuptotal1);

                }
                //if(checkbeginmic == 1 && mFOS != null) {
                //    mFOS.write(mReceiveData1, 29, 720);
                //}
                break;
                case 0x02:
                    //qiang mic total 18 =13
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1qiang mic index"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        break;
                    }
                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex1(indextotalsize,0);
                    byte[] macid = new byte[9];
                    System.arraycopy(mReceiveData1, currentindex + 7, macid, 0, 6);
                    macid[6] = mReceiveData1[currentindex + 5];
                    macid[7] = mReceiveData1[currentindex + 6];
                    macid[8] = (byte) 0x01;
                    int storagesize = gstorage1.size();
                    int nowindex = gstorage1.addOrGetIndex(macid);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal1[0]++;
                    } else {
                        byte[] arr = gstorage1.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x01) {
                            int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                            if (indextotalsize != 1) {
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal1 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal1 += indextotalsize;
                                }
                            }
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal1[0]++;
                        } else {
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal1[0]++;
                        }
                    }

                    int deviceid = ((mReceiveData1[currentindex + 14] & 0xFF) * 256) + (mReceiveData1[currentindex + 13] & 0xFF);
                    int highbit = ((mReceiveData1[currentindex + 15] >> 4) & 0xF);
                    int lowbit = (mReceiveData1[currentindex + 15] & 0xF);

                    int highchannelbit = ((mReceiveData1[currentindex + 16] >> 4) & 0xF);
                    int lowrolebit = (mReceiveData1[currentindex + 16] & 0xF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "上行抢麦" + tempstringoff1));
                    SpiLog.print("spi11", "上行抢麦  " + ByteArraysToString(mReceiveData1, currentindex, currentindex + 17));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x10:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1pair index"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData1[currentindex + 7] & 0xFF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "配对" + tempstringoff1));
                    SpiLog.print("spi11", "配对      " + ByteArraysToString(mReceiveData1, currentindex, currentindex + 8));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x11:
                    //pair sub info
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1pair sub info index"));
                    int msglenSize = (mReceiveData1[currentindex + 4] & 0xFF) * 256 + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);
                    checkTLinkUpIndex1(indextotalsize, 1);
                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData1, currentindex + 7, submacid1, 0, 6);
                    System.arraycopy(mReceiveData1, currentindex + 7, nowsubMac1, 0, 6);
                    int deviceid = ((mReceiveData1[currentindex + 14] & 0xFF) * 256) + (mReceiveData1[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData1[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData1[currentindex + 16] & 0xFF);
                    // revert 32 byte
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "配对上报" + tempstringoff1));
                    SpiLog.print("spi11", "配对上报  " + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x12:
                    //sub heart packag
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1sub heart packag index"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "sub heart packag checksum fail " + (sum & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex1(indextotalsize,2);
                    byte[] submacid1 = new byte[9];
                    System.arraycopy(mReceiveData1, currentindex + 7, submacid1, 0, 6);
                    submacid1[6] = mReceiveData1[currentindex + 5];
                    submacid1[7] = mReceiveData1[currentindex + 6];
                    submacid1[8] = (byte) 0x01;
                    int storagesize = gstorage.size();
                    int nowindex = gstorage.addOrGetIndex(submacid1);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal1[2]++;
                    } else {
                        byte[] arr = gstorage.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x01) {
                            if (indextotalsize != 1) {
                                int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal1 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal1 += indextotalsize;
                                }
                            }
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal1[2]++;
                        } else {
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal1[2]++;
                        }
                    }

                    System.arraycopy(mReceiveData1, currentindex + 7, nowsubMac1, 0, 6);
                    int deviceid = ((mReceiveData1[currentindex + 14] & 0xFF) * 256) + (mReceiveData1[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData1[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData1[currentindex + 16] & 0xFF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "子机心跳" + tempstringoff1));
                    SpiLog.print("spi11", "子机心跳  " + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    // revert 32 byte
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x20:
                    //tx tlink chip sw version
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1tx tlink chip sw version index"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);

                    int versioninfo = (mReceiveData1[currentindex + 7] & 0xFF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "版本" + tempstringoff1));
                    SpiLog.print("spi11", "版本      " + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x21:
                    //tx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1tx tlink chip upgrade index"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);

                    int upgrademode = (mReceiveData1[currentindex + 7] & 0xFF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "升级" + tempstringoff1));
                    SpiLog.print("spi11", "升级      " + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x22:
                    //tx tlink chip enter/exit dut mode
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1tx tlink chip dut index"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);

                    int dutmode = (mReceiveData1[currentindex + 7] & 0xFF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "Dut" + tempstringoff1));
                    SpiLog.print("spi11", "Dut       " + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x30:
                    //sub rx tlink chip setting device number
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1sub rx tlink chip setting device number index"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData1, currentindex + 7, submacid1, 0, 6);
                    int deviceid = ((mReceiveData1[currentindex + 14] & 0xFF) * 256) + (mReceiveData1[currentindex + 13] & 0xFF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 14);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "设备号" + tempstringoff1));
                    SpiLog.print("spi11", "设备号    " + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x31:
                    //sub rx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1sub rx tlink chip upgrade mode index"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData1, currentindex + 7, submacid1, 0, 6);
                    int dutmode = (mReceiveData1[currentindex + 13] & 0xFF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "子升级" + tempstringoff1));
                    SpiLog.print("spi11", "子升级    " + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }

                break;
                case 0x32:
                    //sub rx tlink chip setting rode
                {
                    Log.i(TAG, "parseMessage1 = 32");
                    //runOnUiThread(() -> addText(mTextReceive1, "1sub rx tlink chip setting rode  index"));
                    int msglenSize = (mReceiveData1[currentindex + 4] & 0xFF) * 256 + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage1 sum=" + (sum & 0xFF) + "=" + (currentindex + msglenSize + 4) + "=" + (mReceiveData1[currentindex + msglenSize + 4] & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData1, currentindex + 7, submacid1, 0, 6);
                    int mrole = (mReceiveData1[currentindex + 13] & 0xFF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "子角色" + tempstringoff1));
                    SpiLog.print("spi11", "子角色    " + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x40:
                    //tx tlink send log infos
                {
                    Log.i(TAG, "parseMessage1 = 0x40");
                    //runOnUiThread(() -> addText(mTextReceive1, formatDataTime()+"1tx log infos"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage1 = 0x40 sum=" + (sum & 0xFF));
                        return;
                    }
                    Log.i(TAG, "parseMessage1 = 0x40 2");
                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);

                    int temprftotal = (mReceiveData1[currentindex + 7] & 0xFF) + ((mReceiveData1[currentindex + 8] & 0xFF) << 8) + ((mReceiveData1[currentindex + 9] & 0xFF) << 16) + ((mReceiveData1[currentindex + 10] & 0xFF) << 24);
                    int temprfloss = (mReceiveData1[currentindex + 11] & 0xFF) + ((mReceiveData1[currentindex + 12] & 0xFF) << 8) + ((mReceiveData1[currentindex + 13] & 0xFF) << 16) + ((mReceiveData1[currentindex + 14] & 0xFF) << 24);
                    if (temprftotal != 0) {
                        mTLinkRFIndex1 = temprftotal;
                        mTLinkRFlosstotal1 = temprfloss;
                    }
                    //Log.i(TAG, "="+(mReceiveData1[currentindex+7]& 0xFF) + "="+(mReceiveData1[currentindex+8]& 0xFF) + "="+(mReceiveData1[currentindex+9]& 0xFF)+ "="+(mReceiveData1[currentindex+10]& 0xFF));
                    //Log.i(TAG, "="+(mReceiveData1[currentindex+11]& 0xFF) + "="+(mReceiveData1[currentindex+12]& 0xFF) + "="+(mReceiveData1[currentindex+13]& 0xFF)+ "="+(mReceiveData1[currentindex+14]& 0xFF));
                    mTLinkspidownlosstotal1 = (mReceiveData1[currentindex + 15] & 0xFF) + ((mReceiveData1[currentindex + 16] & 0xFF) * 256);
                    mTLinkindexdownlosstotal1 = (mReceiveData1[currentindex + 17] & 0xFF) + ((mReceiveData1[currentindex + 18] & 0xFF) * 256);
                    localrfrssi1 = (mReceiveData1[currentindex + 19] & 0xFF);
                    romoterfrssi1 = (mReceiveData1[currentindex + 20] & 0xFF);
                    Log.i(TAG, "parseMessage1 mTLinkRFIndex=" + mTLinkRFIndex1 + "=mTLinkRFlosstotal" + mTLinkRFlosstotal1 + "=" + mTLinkspidownlosstotal + "=" + mTLinkindexdownlosstotal1 + "=" + localrfrssi1);
                    //revert 10 byte
                    SpiLog.print("spi1", "Log:" + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    int totalindex = 0;
                    for (int k = 0; k < 9; k++) {
                        totalindex = totalindex + (zhilingindex1[k] - 1);
                    }
                    int totalupindex = 0;
                    for (int n = 0; n < 3; n++) {
                        totalupindex = totalupindex + mTLinkindexuptotal1[n];
                    }
                    //NstartSaveLog("Log:"+ByteArraysToString(mReceiveData1,currentindex,currentindex+msglenSize+4),"spi1",mTLinkRFIndex1,mTLinkRFlosstotal1,mTLinkIndex1-1,mTLinkspidownlosstotal1,mTLinkspiuptotal1,mTLinkspiuplosstotal1,zhilingindex1-1,mTLinkindexdownlosstotal1-1,mTLinkindexuptotal1,mTLinkindexuplosstotal1,localrfrssi1,romoterfrssi1);
                    currentindex += msglenSize + 5;
                    for (int j = 0; j < deviceList.size(); j++) {
                        if (deviceList.get(j).getName().equals("spi1")) {
                            deviceList.set(j, new SpiDeviceModel("spi1", mTLinkRFIndex1, mTLinkRFlosstotal1, mTLinkIndex1 - 1, mTLinkspidownlosstotal1, mTLinkspiuptotal1, mTLinkspiuplosstotal1, totalindex, mTLinkindexdownlosstotal1, totalupindex, mTLinkindexuplosstotal1, localrfrssi1, romoterfrssi1));
                            Log.i(TAG, "parseMessage1 = 0x40 3");
                        }
                    }
                    //NstartSaveLog("spi1",mTLinkRFIndex1,mTLinkRFlosstotal1,mTLinkIndex1-1,mTLinkspidownlosstotal1,mTLinkspiuptotal1,mTLinkspiuplosstotal1,zhilingindex1-1,mTLinkindexdownlosstotal1-1,mTLinkindexuptotal1,mTLinkindexuplosstotal1,localrfrssi1,romoterfrssi1);


                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            adapter.notifyDataSetChanged();
                        }
                    });
                }
                break;
                case 0x4f:
                    //clear log
                {
                    //runOnUiThread(() -> addText(mTextReceive1, "1pair index"));
                    int msglenSize = ((mReceiveData1[currentindex + 4] & 0xFF) * 256) + (mReceiveData1[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData1[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData1[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData1[currentindex + 5] & 0xFF) + ((mReceiveData1[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData1[currentindex + 7] & 0xFF);
                    tempstringoff1 = ByteArraysToString(mReceiveData1, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive1, formatDataTime() + "清log" + tempstringoff1));
                    SpiLog.print("spi11", "清log     " + ByteArraysToString(mReceiveData1, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                default:
                    return;
            }
            if (mReceiveData1.length > (currentindex + 2) && mReceiveData1[currentindex] != 0) {
                threebyte = mReceiveData1[currentindex];
                fourbyte = mReceiveData1[currentindex + 1];
                cmdbyte = mReceiveData1[currentindex + 2];
                Log.i(TAG, "parseMessage1 = 55=" + mReceiveData1[currentindex]);
            } else {
                threebyte = 0;
                fourbyte = 0;
                cmdbyte = 0;
                Log.i(TAG, "parseMessage1 = 66");
            }
            Log.i(TAG, "parseMessage1 = 77");
        }
    }

    public void parseMessage2() throws Exception {
        Log.i(TAG, "parseMessage2 = 0");
        byte onebyte = mReceiveData2[0];
        byte twobyte = mReceiveData2[1];
        byte threebyte = mReceiveData2[2];
        byte fourbyte = mReceiveData2[3];
        byte cmdbyte = mReceiveData2[4];
        int currentindex = 0;
        Log.i(TAG, "parseMessage2 = 1");
        while ((threebyte & 0xFF) == 0xaa && (fourbyte & 0xFF) == 0xcc) {
            switch (cmdbyte & 0xFF) {
                case 0x01:
                    //audio
                {

                    int msglenSize = (mReceiveData2[6] & 0xFF) * 256 + (mReceiveData2[5] & 0xFF);
                    Log.i(TAG, "parseMessage2 = msglenSize " + msglenSize);
                    int sum = 0;
                    for (int i = 4; i <= (msglenSize + 5); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[msglenSize + 6] & 0xFF)) {
                        Log.i(TAG, "parseMessage2 = 3 fail =" + (sum & 0xFF));
                    }
                    currentindex += msglenSize + 7;
                    byte[] macid = new byte[6];
                    System.arraycopy(mReceiveData2, 7, macid, 0, 6);
                    int deviceid = ((mReceiveData2[14] & 0xFF) * 256) + (mReceiveData2[13] & 0xFF);
                    byte[] syninfo = new byte[10];
                    System.arraycopy(mReceiveData2, 15, syninfo, 0, 10);
                    int laninfo = (mReceiveData2[25] & 0xFF);
                    int speechinfo = (mReceiveData2[26] & 0xFF);
                    int soundtotalsize = (mReceiveData2[27] & 0xFF) + ((mReceiveData2[28] & 0xFF) * 256);
                    int checkbeginmic = 0;
                    for (int k = 0; k < 30; k++) {
                        if (mReceiveData2[50 + k] != 0) {
                            checkbeginmic = 1;
                        }
                    }
                    if (checkbeginmic == 1) {
                        if (whichmicbegin != 2) {
                            //runOnUiThread(() -> addText(mTextReceive2, formatDataTime()+"spi2抢麦"));
                            whichmicbegin = 2;
                        }
                        if (whichmicbegin == 2)
                            mStreamAudioTrack2.write(mReceiveData2, 29, 720);
                    }
                    int checkindexslow = (mReceiveData2[27] & 0xFF);
                    int checkindexhigh = (mReceiveData2[28] & 0xFF);
                    int recnowindex = checkindexslow + checkindexhigh * 256;

                    Log.i(TAG, "recnowindex = " + recnowindex + "  receiveData teric count = " + mReceiveData2.length + ", " + ByteArraysToString(mReceiveData2, 0, 850));
                    if (checkbeginmic == 1 && mFOS2 != null) {
                        mFOS2.write(mReceiveData2, 29, 720);
                    }
                    if (mTLinkspiuptotal2 > 1) {
                        if (mTLinkspiuplastIndex2 != 0 && (recnowindex - mTLinkspiuplastIndex2) >= 2) {
                            mTLinkspiuplosstotal2++;
                        }
                        if ((mTLinkspiuplastIndex2 > recnowindex) && ((65536 - mTLinkspiuplastIndex2 >= 2) || (65536 - mTLinkspiuplastIndex2 == 1 && recnowindex >= 1))) {
                            if ((65536 - mTLinkspiuplastIndex2 >= 2)) {
                                mTLinkspiuplosstotal2++;
                            } else {
                                mTLinkspiuplosstotal2++;
                            }
                        }
                    }
                    mTLinkspiuplastIndex2 = recnowindex;
                    mTLinkspiuptotal2++;
                    Log.i(TAG, "mTLinkspiuplosstotal2 = " + mTLinkspiuplosstotal2 + "recnowindex=" + recnowindex + " teric mTLinkspiuplastIndex2 = " + mTLinkspiuplastIndex2 + ", mTLinkspiuptotal2 =" + mTLinkspiuptotal2);

                }
                //if(checkbeginmic == 1 && mFOS != null) {
                //	mFOS.write(mReceiveData2, 29, 720);
                //}
                break;
                case 0x02:
                    //qiang mic total 18 =13
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2qiang mic index"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        break;
                    }
                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex2(indextotalsize,0);
                    byte[] macid = new byte[9];
                    System.arraycopy(mReceiveData2, currentindex + 7, macid, 0, 6);
                    macid[6] = mReceiveData2[currentindex + 5];
                    macid[7] = mReceiveData2[currentindex + 6];
                    macid[8] = (byte) 0x02;
                    int storagesize = gstorage1.size();
                    int nowindex = gstorage1.addOrGetIndex(macid);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal2[0]++;
                    } else {
                        byte[] arr = gstorage1.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x02) {
                            int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                            if (indextotalsize != 1) {
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal2 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal2 += indextotalsize;
                                }
                            }
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal2[0]++;
                        } else {
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal2[0]++;
                        }
                    }
                    int deviceid = ((mReceiveData2[currentindex + 14] & 0xFF) * 256) + (mReceiveData2[currentindex + 13] & 0xFF);
                    int highbit = ((mReceiveData2[currentindex + 15] >> 4) & 0xF);
                    int lowbit = (mReceiveData2[currentindex + 15] & 0xF);

                    int highchannelbit = ((mReceiveData2[currentindex + 16] >> 4) & 0xF);
                    int lowrolebit = (mReceiveData2[currentindex + 16] & 0xF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "上行抢麦" + tempstringoff2));
                    SpiLog.print("spi12", "上行抢麦  " + ByteArraysToString(mReceiveData2, currentindex, currentindex + 17));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x10:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2pair index"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData2[currentindex + 7] & 0xFF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "配对" + tempstringoff2));
                    SpiLog.print("spi12", "配对      " + ByteArraysToString(mReceiveData2, currentindex, currentindex + 8));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x11:
                    //pair sub info
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2pair sub info index"));
                    int msglenSize = (mReceiveData2[currentindex + 4] & 0xFF) * 256 + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);
                    checkTLinkUpIndex2(indextotalsize, 1);
                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData2, currentindex + 7, submacid1, 0, 6);
                    System.arraycopy(mReceiveData2, currentindex + 7, nowsubMac2, 0, 6);
                    int deviceid = ((mReceiveData2[currentindex + 14] & 0xFF) * 256) + (mReceiveData2[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData2[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData2[currentindex + 16] & 0xFF);
                    // revert 32 byte
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "配对上报" + tempstringoff2));
                    SpiLog.print("spi12", "配对上报  " + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x12:
                    //sub heart packag
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2sub heart packag index"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "sub heart packag checksum fail " + (sum & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex2(indextotalsize,2);
                    byte[] submacid1 = new byte[9];
                    System.arraycopy(mReceiveData2, currentindex + 7, submacid1, 0, 6);
                    submacid1[6] = mReceiveData2[currentindex + 5];
                    submacid1[7] = mReceiveData2[currentindex + 6];
                    submacid1[8] = (byte) 0x02;
                    int storagesize = gstorage.size();
                    int nowindex = gstorage.addOrGetIndex(submacid1);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal2[2]++;
                    } else {
                        byte[] arr = gstorage.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x02) {
                            if (indextotalsize != 1) {
                                int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal2 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal2 += indextotalsize;
                                }
                            }
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal2[2]++;
                        } else {
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal2[2]++;
                        }
                    }
                    System.arraycopy(mReceiveData2, currentindex + 7, nowsubMac2, 0, 6);
                    int deviceid = ((mReceiveData2[currentindex + 14] & 0xFF) * 256) + (mReceiveData2[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData2[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData2[currentindex + 16] & 0xFF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "子机心跳" + tempstringoff2));
                    SpiLog.print("spi12", "子机心跳  " + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    // revert 32 byte
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x20:
                    //tx tlink chip sw version
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2tx tlink chip sw version index"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);

                    int versioninfo = (mReceiveData2[currentindex + 7] & 0xFF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "版本" + tempstringoff2));
                    SpiLog.print("spi12", "版本      " + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x21:
                    //tx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2tx tlink chip upgrade index"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);

                    int upgrademode = (mReceiveData2[currentindex + 7] & 0xFF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "升级" + tempstringoff2));
                    SpiLog.print("spi12", "升级      " + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x22:
                    //tx tlink chip enter/exit dut mode
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2tx tlink chip dut index"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);

                    int dutmode = (mReceiveData2[currentindex + 7] & 0xFF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "Dut" + tempstringoff2));
                    SpiLog.print("spi12", "Dut       " + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x30:
                    //sub rx tlink chip setting device number
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2sub rx tlink chip setting device number index"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData2, currentindex + 7, submacid1, 0, 6);
                    int deviceid = ((mReceiveData2[currentindex + 14] & 0xFF) * 256) + (mReceiveData2[currentindex + 13] & 0xFF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 14);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "设备号" + tempstringoff2));
                    SpiLog.print("spi12", "设备号    " + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x31:
                    //sub rx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2sub rx tlink chip upgrade mode index"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData2, currentindex + 7, submacid1, 0, 6);
                    int dutmode = (mReceiveData2[currentindex + 13] & 0xFF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "子升级" + tempstringoff2));
                    SpiLog.print("spi12", "子升级    " + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }

                break;
                case 0x32:
                    //sub rx tlink chip setting rode
                {
                    Log.i(TAG, "parseMessage2 = 32");
                    //runOnUiThread(() -> addText(mTextReceive2, "2sub rx tlink chip setting rode  index"));
                    int msglenSize = (mReceiveData2[currentindex + 4] & 0xFF) * 256 + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage2 sum=" + (sum & 0xFF) + "=" + (currentindex + msglenSize + 4) + "=" + (mReceiveData2[currentindex + msglenSize + 4] & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData2, currentindex + 7, submacid1, 0, 6);
                    int mrole = (mReceiveData2[currentindex + 13] & 0xFF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "子角色" + tempstringoff2));
                    SpiLog.print("spi12", "子角色    " + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x40:
                    //tx tlink send log infos
                {
                    Log.i(TAG, "parseMessage2 = 0x40");
                    //runOnUiThread(() -> addText(mTextReceive2, formatDataTime()+"2tx log infos"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage2 = 0x40 sum=" + (sum & 0xFF));
                        return;
                    }
                    Log.i(TAG, "parseMessage2 = 0x40 2");
                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);

                    int temprftotal = (mReceiveData2[currentindex + 7] & 0xFF) + ((mReceiveData2[currentindex + 8] & 0xFF) << 8) + ((mReceiveData2[currentindex + 9] & 0xFF) << 16) + ((mReceiveData2[currentindex + 10] & 0xFF) << 24);
                    int temprfloss = (mReceiveData2[currentindex + 11] & 0xFF) + ((mReceiveData2[currentindex + 12] & 0xFF) << 8) + ((mReceiveData2[currentindex + 13] & 0xFF) << 16) + ((mReceiveData2[currentindex + 14] & 0xFF) << 24);
                    if (temprftotal != 0) {
                        mTLinkRFIndex2 = temprftotal;
                        mTLinkRFlosstotal2 = temprfloss;
                    }
                    //Log.i(TAG, "="+(mReceiveData2[currentindex+7]& 0xFF) + "="+(mReceiveData2[currentindex+8]& 0xFF) + "="+(mReceiveData2[currentindex+9]& 0xFF)+ "="+(mReceiveData2[currentindex+10]& 0xFF));
                    //Log.i(TAG, "="+(mReceiveData2[currentindex+11]& 0xFF) + "="+(mReceiveData2[currentindex+12]& 0xFF) + "="+(mReceiveData2[currentindex+13]& 0xFF)+ "="+(mReceiveData2[currentindex+14]& 0xFF));
                    mTLinkspidownlosstotal2 = (mReceiveData2[currentindex + 15] & 0xFF) + ((mReceiveData2[currentindex + 16] & 0xFF) * 256);
                    mTLinkindexdownlosstotal2 = (mReceiveData2[currentindex + 17] & 0xFF) + ((mReceiveData2[currentindex + 18] & 0xFF) * 256);
                    localrfrssi2 = (mReceiveData2[currentindex + 19] & 0xFF);
                    romoterfrssi2 = (mReceiveData2[currentindex + 20] & 0xFF);
                    Log.i(TAG, "parseMessage2 mTLinkRFIndex2=" + mTLinkRFIndex2 + "=mTLinkRFlosstotal2" + mTLinkRFlosstotal2 + "=" + mTLinkspidownlosstotal2 + "=" + mTLinkindexdownlosstotal2 + "=" + localrfrssi2);
                    //revert 10 byte
                    SpiLog.print("spi2", "Log:" + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    //NstartSaveLog("Log:"+ByteArraysToString(mReceiveData2,currentindex,currentindex+msglenSize+4),"spi2",mTLinkRFIndex2,mTLinkRFlosstotal2,mTLinkIndex2-1,mTLinkspidownlosstotal2,mTLinkspiuptotal2,mTLinkspiuplosstotal2,zhilingindex2-1,mTLinkindexdownlosstotal2-1,mTLinkindexuptotal2,mTLinkindexuplosstotal2,localrfrssi2,romoterfrssi2);
                    int totalindex = 0;
                    for (int k = 0; k < 9; k++) {
                        totalindex = totalindex + (zhilingindex2[k] - 1);
                    }
                    int totalupindex = 0;
                    for (int n = 0; n < 3; n++) {
                        totalupindex = totalupindex + mTLinkindexuptotal2[n];
                    }
                    currentindex += msglenSize + 5;
                    for (int j = 0; j < deviceList.size(); j++) {
                        if (deviceList.get(j).getName().equals("spi2")) {
                            deviceList.set(j, new SpiDeviceModel("spi2", mTLinkRFIndex2, mTLinkRFlosstotal2, mTLinkIndex2 - 1, mTLinkspidownlosstotal2, mTLinkspiuptotal2, mTLinkspiuplosstotal2, totalindex, mTLinkindexdownlosstotal2, totalupindex, mTLinkindexuplosstotal2, localrfrssi2, romoterfrssi2));
                            Log.i(TAG, "parseMessage2 = 0x40 3");
                        }
                    }
                    //NstartSaveLog("spi2",mTLinkRFIndex2,mTLinkRFlosstotal2,mTLinkIndex2-1,mTLinkspidownlosstotal2,mTLinkspiuptotal2,mTLinkspiuplosstotal2,zhilingindex2-1,mTLinkindexdownlosstotal2,mTLinkindexuptotal2,mTLinkindexuplosstotal2,localrfrssi2,romoterfrssi2);


                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            adapter.notifyDataSetChanged();
                        }
                    });
                }
                break;
                case 0x4f:
                    //clear log
                {
                    //runOnUiThread(() -> addText(mTextReceive2, "2pair index"));
                    int msglenSize = ((mReceiveData2[currentindex + 4] & 0xFF) * 256) + (mReceiveData2[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData2[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData2[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData2[currentindex + 5] & 0xFF) + ((mReceiveData2[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData2[currentindex + 7] & 0xFF);
                    tempstringoff2 = ByteArraysToString(mReceiveData2, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive2, formatDataTime() + "清log" + tempstringoff2));
                    SpiLog.print("spi12", "清log     " + ByteArraysToString(mReceiveData2, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                default:
                    return;
            }
            if (mReceiveData2.length > (currentindex + 2) && mReceiveData2[currentindex] != 0) {
                threebyte = mReceiveData2[currentindex];
                fourbyte = mReceiveData2[currentindex + 1];
                cmdbyte = mReceiveData2[currentindex + 2];
                Log.i(TAG, "parseMessage2 = 55=" + mReceiveData2[currentindex]);
            } else {
                threebyte = 0;
                fourbyte = 0;
                cmdbyte = 0;
                Log.i(TAG, "parseMessage2 = 66");
            }
            Log.i(TAG, "parseMessage2 = 77");
        }
    }

    public void parseMessage3() throws Exception {
        Log.i(TAG, "parseMessage3 = 0");
        byte onebyte = mReceiveData3[0];
        byte twobyte = mReceiveData3[1];
        byte threebyte = mReceiveData3[2];
        byte fourbyte = mReceiveData3[3];
        byte cmdbyte = mReceiveData3[4];
        int currentindex = 0;
        Log.i(TAG, "parseMessage3 = 1");
        while ((threebyte & 0xFF) == 0xaa && (fourbyte & 0xFF) == 0xcc) {
            switch (cmdbyte & 0xFF) {
                case 0x01:
                    //audio
                {

                    int msglenSize = (mReceiveData3[6] & 0xFF) * 256 + (mReceiveData3[5] & 0xFF);
                    Log.i(TAG, "parseMessage3 = msglenSize " + msglenSize);
                    int sum = 0;
                    for (int i = 4; i <= (msglenSize + 5); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[msglenSize + 6] & 0xFF)) {
                        Log.i(TAG, "parseMessage3 = 3 fail =" + (sum & 0xFF));
                    }
                    currentindex += msglenSize + 7;
                    byte[] macid = new byte[6];
                    System.arraycopy(mReceiveData3, 7, macid, 0, 6);
                    int deviceid = ((mReceiveData3[14] & 0xFF) * 256) + (mReceiveData3[13] & 0xFF);
                    byte[] syninfo = new byte[10];
                    System.arraycopy(mReceiveData3, 15, syninfo, 0, 10);
                    int laninfo = (mReceiveData3[25] & 0xFF);
                    int speechinfo = (mReceiveData3[26] & 0xFF);
                    int soundtotalsize = (mReceiveData3[27] & 0xFF) + ((mReceiveData3[28] & 0xFF) * 256);
                    int checkbeginmic = 0;
                    for (int k = 0; k < 30; k++) {
                        if (mReceiveData3[50 + k] != 0) {
                            checkbeginmic = 1;
                        }
                    }
                    if (checkbeginmic == 1) {
                        if (whichmicbegin != 3) {
                            //runOnUiThread(() -> addText(mTextReceive3, formatDataTime()+" spi3抢麦"));
                            whichmicbegin = 3;
                        }
                        if (whichmicbegin == 3)
                            mStreamAudioTrack3.write(mReceiveData3, 29, 720);
                    }
                    int checkindexslow = (mReceiveData3[27] & 0xFF);
                    int checkindexhigh = (mReceiveData3[28] & 0xFF);
                    int recnowindex = checkindexslow + checkindexhigh * 256;

                    Log.i(TAG, "recnowindex = " + recnowindex + "  receiveData teric count = " + mReceiveData3.length + ", " + Arrays.toString(mReceiveData3));
                    if (checkbeginmic == 1 && mFOS3 != null) {
                        mFOS3.write(mReceiveData3, 29, 720);
                    }
                    if (mTLinkspiuptotal3 > 1) {
                        if (mTLinkspiuplastIndex3 != 0 && (recnowindex - mTLinkspiuplastIndex3) >= 2) {
                            mTLinkspiuplosstotal3++;
                        }
                        if ((mTLinkspiuplastIndex3 > recnowindex) && ((65536 - mTLinkspiuplastIndex3 >= 2) || (65536 - mTLinkspiuplastIndex3 == 1 && recnowindex >= 1))) {
                            if ((65536 - mTLinkspiuplastIndex3 >= 2)) {
                                mTLinkspiuplosstotal3++;
                            } else {
                                mTLinkspiuplosstotal3++;
                            }
                        }
                    }
                    mTLinkspiuplastIndex3 = recnowindex;
                    mTLinkspiuptotal3++;
                    Log.i(TAG, "mTLinkspiuplosstotal3 = " + mTLinkspiuplosstotal3 + "recnowindex=" + recnowindex + " teric mTLinkspiuplastIndex3 = " + mTLinkspiuplastIndex3 + ", mTLinkspiuptotal3 =" + mTLinkspiuptotal3);

                }
                //if(checkbeginmic == 1 && mFOS != null) {
                //    mFOS.write(mReceiveData3, 29, 720);
                //}
                break;
                case 0x02:
                    //qiang mic total 18 =13
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3qiang mic index"));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        break;
                    }
                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex3(indextotalsize,0);
                    byte[] macid = new byte[9];
                    System.arraycopy(mReceiveData3, currentindex + 7, macid, 0, 6);
                    macid[6] = mReceiveData3[currentindex + 5];
                    macid[7] = mReceiveData3[currentindex + 6];
                    macid[8] = (byte) 0x03;
                    int storagesize = gstorage1.size();
                    int nowindex = gstorage1.addOrGetIndex(macid);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal3[0]++;
                    } else {
                        byte[] arr = gstorage1.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x03) {
                            int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                            if (indextotalsize != 1) {
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal3 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal3 += indextotalsize;
                                }
                            }
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal3[0]++;
                        } else {
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal3[0]++;
                        }
                    }
                    int deviceid = ((mReceiveData3[currentindex + 14] & 0xFF) * 256) + (mReceiveData3[currentindex + 13] & 0xFF);
                    int highbit = ((mReceiveData3[currentindex + 15] >> 4) & 0xF);
                    int lowbit = (mReceiveData3[currentindex + 15] & 0xF);

                    int highchannelbit = ((mReceiveData3[currentindex + 16] >> 4) & 0xF);
                    int lowrolebit = (mReceiveData3[currentindex + 16] & 0xF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "上行抢麦" + tempstringoff3));
                    SpiLog.print("spi13", "上行抢麦  " + ByteArraysToString(mReceiveData3, currentindex, currentindex + 17));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x10:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3pair index"));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData3[currentindex + 7] & 0xFF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "配对" + tempstringoff3));
                    SpiLog.print("spi13", "配对      " + ByteArraysToString(mReceiveData3, currentindex, currentindex + 8));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x11:
                    //pair sub info
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3pair sub info index"));
                    int msglenSize = (mReceiveData3[currentindex + 4] & 0xFF) * 256 + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);
                    checkTLinkUpIndex3(indextotalsize, 1);
                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData3, currentindex + 7, submacid1, 0, 6);
                    System.arraycopy(mReceiveData3, currentindex + 7, nowsubMac3, 0, 6);
                    int deviceid = ((mReceiveData3[currentindex + 14] & 0xFF) * 256) + (mReceiveData3[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData3[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData3[currentindex + 16] & 0xFF);
                    // revert 32 byte
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "配对上报" + tempstringoff3));
                    SpiLog.print("spi13", "配对上报  " + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x12:
                    //sub heart packag
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3sub heart packag index"));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "sub heart packag checksum fail " + (sum & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex3(indextotalsize,2);
                    byte[] submacid1 = new byte[9];
                    System.arraycopy(mReceiveData3, currentindex + 7, submacid1, 0, 6);
                    submacid1[6] = mReceiveData3[currentindex + 5];
                    submacid1[7] = mReceiveData3[currentindex + 6];
                    submacid1[8] = (byte) 0x03;
                    int storagesize = gstorage.size();
                    int nowindex = gstorage.addOrGetIndex(submacid1);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal3[2]++;
                    } else {
                        byte[] arr = gstorage.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x03) {
                            if (indextotalsize != 1) {
                                int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal3 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal3 += indextotalsize;
                                }
                            }
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal3[2]++;
                        } else {
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal3[2]++;
                        }
                    }
                    System.arraycopy(mReceiveData3, currentindex + 7, nowsubMac3, 0, 6);
                    int deviceid = ((mReceiveData3[currentindex + 14] & 0xFF) * 256) + (mReceiveData3[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData3[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData3[currentindex + 16] & 0xFF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "子机心跳" + tempstringoff3));
                    SpiLog.print("spi13", "子机心跳  " + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    // revert 32 byte
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x20:
                    //tx tlink chip sw version
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3tx tlink chip sw version index"));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);

                    int versioninfo = (mReceiveData3[currentindex + 7] & 0xFF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "版本" + tempstringoff3));
                    SpiLog.print("spi13", "版本      " + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x21:
                    //tx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3tx tlink chip upgrade index"));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);

                    int upgrademode = (mReceiveData3[currentindex + 7] & 0xFF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "升级" + tempstringoff3));
                    SpiLog.print("spi13", "升级      " + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x22:
                    //tx tlink chip enter/exit dut mode
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3tx tlink chip dut index"));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);

                    int dutmode = (mReceiveData3[currentindex + 7] & 0xFF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "Dut" + tempstringoff3));
                    SpiLog.print("spi13", "Dut       " + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x30:
                    //sub rx tlink chip setting device number
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3sub rx tlink chip setting device number index"));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData3, currentindex + 7, submacid1, 0, 6);
                    int deviceid = ((mReceiveData3[currentindex + 14] & 0xFF) * 256) + (mReceiveData3[currentindex + 13] & 0xFF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 14);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "设备号" + tempstringoff3));
                    SpiLog.print("spi13", "设备号    " + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x31:
                    //sub rx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3sub rx tlink chip upgrade mode index"));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData3, currentindex + 7, submacid1, 0, 6);
                    int dutmode = (mReceiveData3[currentindex + 13] & 0xFF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "子升级" + tempstringoff3));
                    SpiLog.print("spi13", "子升级    " + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }

                break;
                case 0x32:
                    //sub rx tlink chip setting rode
                {
                    Log.i(TAG, "parseMessage3 = 32");
                    //runOnUiThread(() -> addText(mTextReceive3, "3sub rx tlink chip setting rode  index"));
                    int msglenSize = (mReceiveData3[currentindex + 4] & 0xFF) * 256 + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage3 sum=" + (sum & 0xFF) + "=" + (currentindex + msglenSize + 4) + "=" + (mReceiveData3[currentindex + msglenSize + 4] & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData3, currentindex + 7, submacid1, 0, 6);
                    int mrole = (mReceiveData3[currentindex + 13] & 0xFF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "子角色" + tempstringoff3));
                    SpiLog.print("spi13", "子角色    " + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x40:
                    //tx tlink send log infos
                {
                    Log.i(TAG, "parseMessage3 = 0x40");
                    //runOnUiThread(() -> addText(mTextReceive3, formatDataTime()+"3tx  log infos "));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage3 = 0x40 sum=" + (sum & 0xFF));
                        return;
                    }
                    Log.i(TAG, "parseMessage3 = 0x40 2");
                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);

                    int temprftotal = (mReceiveData3[currentindex + 7] & 0xFF) + ((mReceiveData3[currentindex + 8] & 0xFF) << 8) + ((mReceiveData3[currentindex + 9] & 0xFF) << 16) + ((mReceiveData3[currentindex + 10] & 0xFF) << 24);
                    int temprfloss = (mReceiveData3[currentindex + 11] & 0xFF) + ((mReceiveData3[currentindex + 12] & 0xFF) << 8) + ((mReceiveData3[currentindex + 13] & 0xFF) << 16) + ((mReceiveData3[currentindex + 14] & 0xFF) << 24);
                    if (temprftotal != 0) {
                        mTLinkRFIndex3 = temprftotal;
                        mTLinkRFlosstotal3 = temprfloss;
                    }
                    //Log.i(TAG, "="+(mReceiveData3[currentindex+7]& 0xFF) + "="+(mReceiveData3[currentindex+8]& 0xFF) + "="+(mReceiveData3[currentindex+9]& 0xFF)+ "="+(mReceiveData3[currentindex+10]& 0xFF));
                    //Log.i(TAG, "="+(mReceiveData3[currentindex+11]& 0xFF) + "="+(mReceiveData3[currentindex+12]& 0xFF) + "="+(mReceiveData3[currentindex+13]& 0xFF)+ "="+(mReceiveData3[currentindex+14]& 0xFF));
                    mTLinkspidownlosstotal3 = (mReceiveData3[currentindex + 15] & 0xFF) + ((mReceiveData3[currentindex + 16] & 0xFF) * 256);
                    mTLinkindexdownlosstotal3 = (mReceiveData3[currentindex + 17] & 0xFF) + ((mReceiveData3[currentindex + 18] & 0xFF) * 256);
                    localrfrssi3 = (mReceiveData3[currentindex + 19] & 0xFF);
                    romoterfrssi3 = (mReceiveData3[currentindex + 20] & 0xFF);
                    Log.i(TAG, "parseMessage3 mTLinkRFIndex3=" + mTLinkRFIndex3 + "=mTLinkRFlosstotal3" + mTLinkRFlosstotal3 + "=" + mTLinkspidownlosstotal3 + "=" + mTLinkindexdownlosstotal3 + "=" + localrfrssi3);
                    //revert 10 byte
                    SpiLog.print("spi3", "Log:" + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    //NstartSaveLog("Log:"+ByteArraysToString(mReceiveData3,currentindex,currentindex+msglenSize+4),"spi3",mTLinkRFIndex3,mTLinkRFlosstotal3,mTLinkIndex3-1,mTLinkspidownlosstotal3,mTLinkspiuptotal3,mTLinkspiuplosstotal3,zhilingindex3-1,mTLinkindexdownlosstotal3-1,mTLinkindexuptotal3,mTLinkindexuplosstotal3,localrfrssi3,romoterfrssi3);
                    int totalindex = 0;
                    for (int k = 0; k < 9; k++) {
                        totalindex = totalindex + (zhilingindex3[k] - 1);
                    }
                    int totalupindex = 0;
                    for (int n = 0; n < 3; n++) {
                        totalupindex = totalupindex + mTLinkindexuptotal3[n];
                    }
                    currentindex += msglenSize + 5;
                    for (int j = 0; j < deviceList.size(); j++) {
                        if (deviceList.get(j).getName().equals("spi3")) {
                            deviceList.set(j, new SpiDeviceModel("spi3", mTLinkRFIndex3, mTLinkRFlosstotal3, mTLinkIndex3 - 1, mTLinkspidownlosstotal3, mTLinkspiuptotal3, mTLinkspiuplosstotal3, totalindex, mTLinkindexdownlosstotal3, totalupindex, mTLinkindexuplosstotal3, localrfrssi3, romoterfrssi3));
                            Log.i(TAG, "parseMessage3 = 0x40 3");
                        }
                    }
                    //NstartSaveLog("spi3",mTLinkRFIndex3,mTLinkRFlosstotal3,mTLinkIndex3-1,mTLinkspidownlosstotal3,mTLinkspiuptotal3,mTLinkspiuplosstotal3,zhilingindex3-1,mTLinkindexdownlosstotal3-1,mTLinkindexuptotal3,mTLinkindexuplosstotal3,localrfrssi3,romoterfrssi3);


                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            adapter.notifyDataSetChanged();
                        }
                    });
                }
                break;
                case 0x4f:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive3, "3pair index"));
                    int msglenSize = ((mReceiveData3[currentindex + 4] & 0xFF) * 256) + (mReceiveData3[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData3[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData3[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData3[currentindex + 5] & 0xFF) + ((mReceiveData3[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData3[currentindex + 7] & 0xFF);
                    tempstringoff3 = ByteArraysToString(mReceiveData3, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive3, formatDataTime() + "清log" + tempstringoff3));
                    SpiLog.print("spi13", "清log     " + ByteArraysToString(mReceiveData3, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                default:
                    return;
            }
            if (mReceiveData3.length > (currentindex + 2) && mReceiveData3[currentindex] != 0) {
                threebyte = mReceiveData3[currentindex];
                fourbyte = mReceiveData3[currentindex + 1];
                cmdbyte = mReceiveData3[currentindex + 2];
                Log.i(TAG, "parseMessage3 = 55=" + mReceiveData3[currentindex]);
            } else {
                threebyte = 0;
                fourbyte = 0;
                cmdbyte = 0;
                Log.i(TAG, "parseMessage3 = 66");
            }
            Log.i(TAG, "parseMessage3 = 77");
        }
    }

    public void parseMessage4() throws Exception {
        Log.i(TAG, "parseMessage4 = 0");
        byte onebyte = mReceiveData4[0];
        byte twobyte = mReceiveData4[1];
        byte threebyte = mReceiveData4[2];
        byte fourbyte = mReceiveData4[3];
        byte cmdbyte = mReceiveData4[4];
        int currentindex = 0;
        Log.i(TAG, "parseMessage4 = 1");
        while ((threebyte & 0xFF) == 0xaa && (fourbyte & 0xFF) == 0xcc) {
            switch (cmdbyte & 0xFF) {
                case 0x01:
                    //audio
                {

                    int msglenSize = (mReceiveData4[6] & 0xFF) * 256 + (mReceiveData4[5] & 0xFF);

                    Log.i(TAG, "parseMessage4 = msglenSize " + msglenSize);
                    int sum = 0;
                    for (int i = 4; i <= (msglenSize + 5); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[msglenSize + 6] & 0xFF)) {
                        Log.i(TAG, "parseMessage4 = 3 fail =" + (sum & 0xFF));
                    }
                    currentindex += msglenSize + 7;
                    byte[] macid = new byte[6];
                    System.arraycopy(mReceiveData4, 7, macid, 0, 6);
                    int deviceid = ((mReceiveData4[14] & 0xFF) * 256) + (mReceiveData4[13] & 0xFF);
                    byte[] syninfo = new byte[10];
                    System.arraycopy(mReceiveData4, 15, syninfo, 0, 10);
                    int laninfo = (mReceiveData4[25] & 0xFF);
                    int speechinfo = (mReceiveData4[26] & 0xFF);
                    int soundtotalsize = (mReceiveData4[27] & 0xFF) + ((mReceiveData4[28] & 0xFF) * 256);
                    int checkbeginmic = 0;
                    for (int k = 0; k < 30; k++) {
                        if (mReceiveData4[50 + k] != 0) {
                            checkbeginmic = 1;
                        }
                    }
                    if (checkbeginmic == 1) {
                        if (whichmicbegin != 4) {
                            //runOnUiThread(() -> addText(mTextReceive4, formatDataTime()+" spi4抢麦"));
                            whichmicbegin = 4;
                        }
                        if (whichmicbegin == 4)
                            mStreamAudioTrack4.write(mReceiveData4, 29, 720);
                    }
                    int checkindexslow = (mReceiveData4[27] & 0xFF);
                    int checkindexhigh = (mReceiveData4[28] & 0xFF);
                    int recnowindex = checkindexslow + checkindexhigh * 256;

                    Log.i(TAG, "recnowindex = " + recnowindex + "  receiveData teric count = " + mReceiveData4.length + ", " + Arrays.toString(mReceiveData4));
                    if (checkbeginmic == 1 && mFOS4 != null) {
                        mFOS4.write(mReceiveData4, 29, 720);
                    }
                    if (mTLinkspiuptotal4 > 1) {
                        if (mTLinkspiuplastIndex4 != 0 && (recnowindex - mTLinkspiuplastIndex4) >= 2) {
                            mTLinkspiuplosstotal4++;
                        }
                        if ((mTLinkspiuplastIndex4 > recnowindex) && ((65536 - mTLinkspiuplastIndex4 >= 2) || (65536 - mTLinkspiuplastIndex4 == 1 && recnowindex >= 1))) {
                            if ((65536 - mTLinkspiuplastIndex4 >= 2)) {
                                mTLinkspiuplosstotal4++;
                            } else {
                                mTLinkspiuplosstotal4++;
                            }
                        }
                    }
                    mTLinkspiuplastIndex4 = recnowindex;
                    mTLinkspiuptotal4++;
                    Log.i(TAG, "mTLinkspiuplosstota4 = " + mTLinkspiuplosstotal4 + "recnowindex=" + recnowindex + " teric mTLinkspiuplastIndex4 = " + mTLinkspiuplastIndex4 + ", mTLinkspiuptotal4 =" + mTLinkspiuptotal4);

                }
                //if(checkbeginmic == 1 && mFOS != null) {
                //	mFOS.write(mReceiveData4, 29, 720);
                //}
                break;
                case 0x02:
                    //qiang mic total 18 =13
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4qiang mic index"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        break;
                    }
                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex4(indextotalsize,0);
                    byte[] macid = new byte[9];
                    System.arraycopy(mReceiveData4, currentindex + 7, macid, 0, 6);
                    macid[6] = mReceiveData4[currentindex + 5];
                    macid[7] = mReceiveData4[currentindex + 6];
                    macid[8] = (byte) 0x04;
                    int storagesize = gstorage1.size();
                    int nowindex = gstorage1.addOrGetIndex(macid);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal4[0]++;
                    } else {
                        byte[] arr = gstorage1.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x04) {
                            int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                            if (indextotalsize != 1) {
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal4 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal4 += indextotalsize;
                                }
                            }
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal4[0]++;
                        } else {
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal4[0]++;
                        }
                    }
                    int deviceid = ((mReceiveData4[currentindex + 14] & 0xFF) * 256) + (mReceiveData4[currentindex + 13] & 0xFF);
                    int highbit = ((mReceiveData4[currentindex + 15] >> 4) & 0xF);
                    int lowbit = (mReceiveData4[currentindex + 15] & 0xF);

                    int highchannelbit = ((mReceiveData4[currentindex + 16] >> 4) & 0xF);
                    int lowrolebit = (mReceiveData4[currentindex + 16] & 0xF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "上行抢麦" + tempstringoff4));
                    SpiLog.print("spi14", "上行抢麦  " + ByteArraysToString(mReceiveData4, currentindex, currentindex + 17));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x10:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4pair index"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData4[currentindex + 7] & 0xFF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "配对" + tempstringoff4));
                    SpiLog.print("spi14", "配对      " + ByteArraysToString(mReceiveData4, currentindex, currentindex + 8));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x11:
                    //pair sub info
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4pair sub info index"));
                    int msglenSize = (mReceiveData4[currentindex + 4] & 0xFF) * 256 + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);
                    checkTLinkUpIndex4(indextotalsize, 1);
                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData4, currentindex + 7, submacid1, 0, 6);
                    System.arraycopy(mReceiveData4, currentindex + 7, nowsubMac4, 0, 6);
                    int deviceid = ((mReceiveData4[currentindex + 14] & 0xFF) * 256) + (mReceiveData4[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData4[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData4[currentindex + 16] & 0xFF);
                    // revert 32 byte
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "配对上报" + tempstringoff4));
                    SpiLog.print("spi14", "配对上报  " + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x12:
                    //sub heart packag
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4sub heart packag index"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "sub heart packag checksum fail " + (sum & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex4(indextotalsize,2);
                    byte[] submacid1 = new byte[9];
                    System.arraycopy(mReceiveData4, currentindex + 7, submacid1, 0, 6);
                    submacid1[6] = mReceiveData4[currentindex + 5];
                    submacid1[7] = mReceiveData4[currentindex + 6];
                    submacid1[8] = (byte) 0x04;
                    int storagesize = gstorage.size();
                    int nowindex = gstorage.addOrGetIndex(submacid1);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal4[2]++;
                    } else {
                        byte[] arr = gstorage.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x04) {
                            if (indextotalsize != 1) {
                                int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal4 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal4 += indextotalsize;
                                }
                            }
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal4[2]++;
                        } else {
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal4[2]++;
                        }
                    }
                    System.arraycopy(mReceiveData4, currentindex + 7, nowsubMac4, 0, 6);
                    int deviceid = ((mReceiveData4[currentindex + 14] & 0xFF) * 256) + (mReceiveData4[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData4[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData4[currentindex + 16] & 0xFF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "子机心跳" + tempstringoff4));
                    SpiLog.print("spi14", "子机心跳  " + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    // revert 32 byte
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x20:
                    //tx tlink chip sw version
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4tx tlink chip sw version index"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);

                    int versioninfo = (mReceiveData4[currentindex + 7] & 0xFF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "版本" + tempstringoff4));
                    SpiLog.print("spi14", "版本      " + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x21:
                    //tx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4tx tlink chip upgrade index"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);

                    int upgrademode = (mReceiveData4[currentindex + 7] & 0xFF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "升级" + tempstringoff4));
                    SpiLog.print("spi14", "升级      " + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x22:
                    //tx tlink chip enter/exit dut mode
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4tx tlink chip dut index"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);

                    int dutmode = (mReceiveData4[currentindex + 7] & 0xFF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "Dut" + tempstringoff4));
                    SpiLog.print("spi14", "Dut       " + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x30:
                    //sub rx tlink chip setting device number
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4sub rx tlink chip setting device number index"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData4, currentindex + 7, submacid1, 0, 6);
                    int deviceid = ((mReceiveData4[currentindex + 14] & 0xFF) * 256) + (mReceiveData4[currentindex + 13] & 0xFF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 14);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "设备号" + tempstringoff4));
                    SpiLog.print("spi14", "设备号    " + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x31:
                    //sub rx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4sub rx tlink chip upgrade mode index"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData4, currentindex + 7, submacid1, 0, 6);
                    int dutmode = (mReceiveData4[currentindex + 13] & 0xFF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "子升级" + tempstringoff4));
                    SpiLog.print("spi14", "子升级    " + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                    //runOnUiThread(() -> addText(mTextReceive4, formatDataTime()+" 4sub rx tlink chip upgrade mode ="+dutmode));
                }

                break;
                case 0x32:
                    //sub rx tlink chip setting rode
                {
                    Log.i(TAG, "parseMessage4 = 32");
                    //runOnUiThread(() -> addText(mTextReceive4, "4sub rx tlink chip setting rode  index"));
                    int msglenSize = (mReceiveData4[currentindex + 4] & 0xFF) * 256 + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage4 sum=" + (sum & 0xFF) + "=" + (currentindex + msglenSize + 4) + "=" + (mReceiveData4[currentindex + msglenSize + 4] & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData4, currentindex + 7, submacid1, 0, 6);
                    int mrole = (mReceiveData4[currentindex + 13] & 0xFF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "子角色" + tempstringoff4));
                    SpiLog.print("spi14", "子角色    " + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                    //runOnUiThread(() -> addText(mTextReceive4, formatDataTime()+" 4分配子机角色"+Arrays.toString(submacid1)+"="+mrole));
                }
                break;
                case 0x40:
                    //tx tlink send log infos
                {
                    Log.i(TAG, "parseMessage4 = 0x40");
                    //runOnUiThread(() -> addText(mTextReceive4, formatDataTime()+"4tx log infos"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage4 = 0x40 sum=" + (sum & 0xFF));
                        return;
                    }
                    Log.i(TAG, "parseMessage4 = 0x40 2");
                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);

                    int temprftotal = (mReceiveData4[currentindex + 7] & 0xFF) + ((mReceiveData4[currentindex + 8] & 0xFF) << 8) + ((mReceiveData4[currentindex + 9] & 0xFF) << 16) + ((mReceiveData4[currentindex + 10] & 0xFF) << 24);
                    int temprfloss = (mReceiveData4[currentindex + 11] & 0xFF) + ((mReceiveData4[currentindex + 12] & 0xFF) << 8) + ((mReceiveData4[currentindex + 13] & 0xFF) << 16) + ((mReceiveData4[currentindex + 14] & 0xFF) << 24);
                    if (temprftotal != 0) {
                        mTLinkRFIndex4 = temprftotal;
                        mTLinkRFlosstotal4 = temprfloss;
                    }
                    //Log.i(TAG, "="+(mReceiveData4[currentindex+7]& 0xFF) + "="+(mReceiveData4[currentindex+8]& 0xFF) + "="+(mReceiveData4[currentindex+9]& 0xFF)+ "="+(mReceiveData4[currentindex+10]& 0xFF));
                    //Log.i(TAG, "="+(mReceiveData4[currentindex+11]& 0xFF) + "="+(mReceiveData4[currentindex+12]& 0xFF) + "="+(mReceiveData4[currentindex+13]& 0xFF)+ "="+(mReceiveData4[currentindex+14]& 0xFF));
                    mTLinkspidownlosstotal4 = (mReceiveData4[currentindex + 15] & 0xFF) + ((mReceiveData4[currentindex + 16] & 0xFF) * 256);
                    mTLinkindexdownlosstotal4 = (mReceiveData4[currentindex + 17] & 0xFF) + ((mReceiveData4[currentindex + 18] & 0xFF) * 256);
                    localrfrssi4 = (mReceiveData4[currentindex + 19] & 0xFF);
                    romoterfrssi4 = (mReceiveData4[currentindex + 20] & 0xFF);
                    Log.i(TAG, "parseMessage4 mTLinkRFIndex4=" + mTLinkRFIndex4 + "=mTLinkRFlosstotal4" + mTLinkRFlosstotal4 + "=" + mTLinkspidownlosstotal4 + "=" + mTLinkindexdownlosstotal4 + "=" + localrfrssi4);
                    //revert 10 byte
                    SpiLog.print("spi4", "Log:" + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    //NstartSaveLog("Log:"+ByteArraysToString(mReceiveData4,currentindex,currentindex+msglenSize+4),"spi4",mTLinkRFIndex4,mTLinkRFlosstotal4,mTLinkIndex4-1,mTLinkspidownlosstotal4,mTLinkspiuptotal4,mTLinkspiuplosstotal4,zhilingindex4-1,mTLinkindexdownlosstotal4-1,mTLinkindexuptotal4,mTLinkindexuplosstotal4,localrfrssi4,romoterfrssi4);
                    currentindex += msglenSize + 5;
                    int totalindex = 0;
                    for (int k = 0; k < 9; k++) {
                        totalindex = totalindex + (zhilingindex4[k] - 1);
                    }
                    int totalupindex = 0;
                    for (int n = 0; n < 3; n++) {
                        totalupindex = totalupindex + mTLinkindexuptotal4[n];
                    }
                    for (int j = 0; j < deviceList.size(); j++) {
                        if (deviceList.get(j).getName().equals("spi4")) {
                            deviceList.set(j, new SpiDeviceModel("spi4", mTLinkRFIndex4, mTLinkRFlosstotal4, mTLinkIndex4 - 1, mTLinkspidownlosstotal4, mTLinkspiuptotal4, mTLinkspiuplosstotal4, totalindex, mTLinkindexdownlosstotal4, totalupindex, mTLinkindexuplosstotal4, localrfrssi4, romoterfrssi4));
                            Log.i(TAG, "parseMessage4 = 0x40 3");
                        }
                    }
                    // NstartSaveLog("spi4",mTLinkRFIndex4,mTLinkRFlosstotal4,mTLinkIndex4-1,mTLinkspidownlosstotal4,mTLinkspiuptotal4,mTLinkspiuplosstotal4,zhilingindex4-1,mTLinkindexdownlosstotal4-1,mTLinkindexuptotal4,mTLinkindexuplosstotal4,localrfrssi4,romoterfrssi4);


                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            adapter.notifyDataSetChanged();
                        }
                    });
                }
                break;
                case 0x4f:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive4, "4pair index"));
                    int msglenSize = ((mReceiveData4[currentindex + 4] & 0xFF) * 256) + (mReceiveData4[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData4[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData4[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData4[currentindex + 5] & 0xFF) + ((mReceiveData4[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData4[currentindex + 7] & 0xFF);
                    tempstringoff4 = ByteArraysToString(mReceiveData4, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive4, formatDataTime() + "清log" + tempstringoff4));
                    SpiLog.print("spi14", "清log     " + ByteArraysToString(mReceiveData4, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                default:
                    return;
            }
            if (mReceiveData4.length > (currentindex + 2) && mReceiveData4[currentindex] != 0) {
                threebyte = mReceiveData4[currentindex];
                fourbyte = mReceiveData4[currentindex + 1];
                cmdbyte = mReceiveData4[currentindex + 2];
                Log.i(TAG, "parseMessage4 = 55=" + mReceiveData4[currentindex]);
            } else {
                threebyte = 0;
                fourbyte = 0;
                cmdbyte = 0;
                Log.i(TAG, "parseMessage4 = 66");
            }
            Log.i(TAG, "parseMessage4 = 77");
        }
    }

    public void parseMessage5() throws Exception {
        Log.i(TAG, "parseMessage5 = 0");
        byte onebyte = mReceiveData5[0];
        byte twobyte = mReceiveData5[1];
        byte threebyte = mReceiveData5[2];
        byte fourbyte = mReceiveData5[3];
        byte cmdbyte = mReceiveData5[4];
        int currentindex = 0;
        Log.i(TAG, "parseMessage5 = 1");
        while ((threebyte & 0xFF) == 0xaa && (fourbyte & 0xFF) == 0xcc) {
            switch (cmdbyte & 0xFF) {
                case 0x01:
                    //audio
                {

                    int msglenSize = (mReceiveData5[6] & 0xFF) * 256 + (mReceiveData5[5] & 0xFF);
                    Log.i(TAG, "parseMessage5 = msglenSize " + msglenSize);
                    int sum = 0;
                    for (int i = 4; i <= (msglenSize + 5); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[msglenSize + 6] & 0xFF)) {
                        Log.i(TAG, "parseMessage5 = 3 fail =" + (sum & 0xFF));
                    }
                    currentindex += msglenSize + 7;
                    byte[] macid = new byte[6];
                    System.arraycopy(mReceiveData5, 7, macid, 0, 6);
                    int deviceid = ((mReceiveData5[14] & 0xFF) * 256) + (mReceiveData5[13] & 0xFF);
                    byte[] syninfo = new byte[10];
                    System.arraycopy(mReceiveData5, 15, syninfo, 0, 10);
                    int laninfo = (mReceiveData5[25] & 0xFF);
                    int speechinfo = (mReceiveData5[26] & 0xFF);
                    int soundtotalsize = (mReceiveData5[27] & 0xFF) + ((mReceiveData5[28] & 0xFF) * 256);
                    int checkbeginmic = 0;
                    for (int k = 0; k < 30; k++) {
                        if (mReceiveData5[50 + k] != 0) {
                            checkbeginmic = 1;
                        }
                    }
                    if (checkbeginmic == 1) {
                        if (whichmicbegin != 5) {
                            //runOnUiThread(() -> addText(mTextReceive5, formatDataTime()+" spi5抢麦"));
                            whichmicbegin = 5;
                        }
                        if (whichmicbegin == 5)
                            mStreamAudioTrack5.write(mReceiveData5, 29, 720);
                    }
                    int checkindexslow = (mReceiveData5[27] & 0xFF);
                    int checkindexhigh = (mReceiveData5[28] & 0xFF);
                    int recnowindex = checkindexslow + checkindexhigh * 256;

                    Log.i(TAG, "recnowindex = " + recnowindex + "  receiveData teric count = " + mReceiveData5.length + ", " + Arrays.toString(mReceiveData5));
                    if (checkbeginmic == 1 && mFOS5 != null) {
                        mFOS5.write(mReceiveData5, 29, 720);
                    }
                    if (mTLinkspiuptotal5 > 1) {
                        if (mTLinkspiuplastIndex5 != 0 && (recnowindex - mTLinkspiuplastIndex5) >= 2) {
                            mTLinkspiuplosstotal5++;
                        }
                        if ((mTLinkspiuplastIndex5 > recnowindex) && ((65536 - mTLinkspiuplastIndex5 >= 2) || (65536 - mTLinkspiuplastIndex5 == 1 && recnowindex >= 1))) {
                            if ((65536 - mTLinkspiuplastIndex5 >= 2)) {
                                mTLinkspiuplosstotal5++;
                            } else {
                                mTLinkspiuplosstotal5++;
                            }
                        }
                    }
                    mTLinkspiuplastIndex5 = recnowindex;
                    mTLinkspiuptotal5++;
                    Log.i(TAG, "mTLinkspiuplosstota5 = " + mTLinkspiuplosstotal5 + "recnowindex=" + recnowindex + " teric mTLinkspiuplastIndex5 = " + mTLinkspiuplastIndex5 + ", mTLinkspiuptota5 =" + mTLinkspiuptotal5);

                }
                //if(checkbeginmic == 1 && mFOS != null) {
                //	mFOS.write(mReceiveData5, 29, 720);
                //}
                break;
                case 0x02:
                    //qiang mic total 18 =13
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5qiang mic index"));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        break;
                    }
                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex5(indextotalsize,0);
                    byte[] macid = new byte[9];
                    System.arraycopy(mReceiveData5, currentindex + 7, macid, 0, 6);
                    macid[6] = mReceiveData5[currentindex + 5];
                    macid[7] = mReceiveData5[currentindex + 6];
                    macid[8] = (byte) 0x05;
                    int storagesize = gstorage1.size();
                    int nowindex = gstorage1.addOrGetIndex(macid);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal5[0]++;
                    } else {
                        byte[] arr = gstorage1.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x05) {
                            int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                            if (indextotalsize != 1) {
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal5 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal5 += indextotalsize;
                                }
                            }
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal5[0]++;
                        } else {
                            gstorage1.getStorage().set(nowindex, macid);
                            mTLinkindexuptotal5[0]++;
                        }
                    }
                    int deviceid = ((mReceiveData5[currentindex + 14] & 0xFF) * 256) + (mReceiveData5[currentindex + 13] & 0xFF);
                    int highbit = ((mReceiveData5[currentindex + 15] >> 4) & 0xF);
                    int lowbit = (mReceiveData5[currentindex + 15] & 0xF);

                    int highchannelbit = ((mReceiveData5[currentindex + 16] >> 4) & 0xF);
                    int lowrolebit = (mReceiveData5[currentindex + 16] & 0xF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "上行抢麦" + tempstringoff5));
                    SpiLog.print("spi15", "上行抢麦  " + ByteArraysToString(mReceiveData5, currentindex, currentindex + 17));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x10:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5pair index"));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData5[currentindex + 7] & 0xFF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "配对" + tempstringoff5));
                    SpiLog.print("spi15", "配对      " + ByteArraysToString(mReceiveData5, currentindex, currentindex + 8));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x11:
                    //pair sub info
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5pair sub info index"));
                    int msglenSize = (mReceiveData5[currentindex + 4] & 0xFF) * 256 + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);
                    checkTLinkUpIndex5(indextotalsize, 1);
                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData5, currentindex + 7, submacid1, 0, 6);
                    System.arraycopy(mReceiveData5, currentindex + 7, nowsubMac5, 0, 6);
                    int deviceid = ((mReceiveData5[currentindex + 14] & 0xFF) * 256) + (mReceiveData5[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData5[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData5[currentindex + 16] & 0xFF);
                    // revert 32 byte
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "配对上报" + tempstringoff5));
                    SpiLog.print("spi15", "配对上报  " + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;

                }
                break;
                case 0x12:
                    //sub heart packag
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5sub heart packag index"));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "sub heart packag checksum fail " + (sum & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);
                    //checkTLinkUpIndex5(indextotalsize,2);
                    byte[] submacid1 = new byte[9];
                    System.arraycopy(mReceiveData5, currentindex + 7, submacid1, 0, 6);
                    submacid1[6] = mReceiveData5[currentindex + 5];
                    submacid1[7] = mReceiveData5[currentindex + 6];
                    submacid1[8] = (byte) 0x05;
                    int storagesize = gstorage.size();
                    int nowindex = gstorage.addOrGetIndex(submacid1);
                    if (nowindex >= storagesize) {
                        mTLinkindexuptotal5[2]++;
                    } else {
                        byte[] arr = gstorage.getStorage().get(nowindex);
                        if ((arr[8] & 0xff) == 0x05) {
                            if (indextotalsize != 1) {
                                int lastindex = (arr[6] & 0xFF) + ((arr[7] & 0xFF) * 256);
                                if (lastindex != 0 && (indextotalsize - lastindex) >= 2) {
                                    mTLinkindexuplosstotal5 += indextotalsize - lastindex - 1;
                                }
                                if (lastindex > indextotalsize && ((65536 - lastindex >= 2) || (65536 - lastindex == 1 && indextotalsize >= 1))) {
                                    mTLinkindexuplosstotal5 += indextotalsize;
                                }
                            }
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal5[2]++;
                        } else {
                            gstorage.getStorage().set(nowindex, submacid1);
                            mTLinkindexuptotal5[2]++;
                        }
                    }
                    System.arraycopy(mReceiveData5, currentindex + 7, nowsubMac5, 0, 6);
                    int deviceid = ((mReceiveData5[currentindex + 14] & 0xFF) * 256) + (mReceiveData5[currentindex + 13] & 0xFF);
                    int batpercent = (mReceiveData5[currentindex + 15] & 0xFF);
                    int versioninfo = (mReceiveData5[currentindex + 16] & 0xFF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 16);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "子机心跳" + tempstringoff5));
                    SpiLog.print("spi15", "子机心跳  " + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    // revert 32 byte
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x20:
                    //tx tlink chip sw version
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5tx tlink chip sw version index"));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);

                    int versioninfo = (mReceiveData5[currentindex + 7] & 0xFF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "版本" + tempstringoff5));
                    SpiLog.print("spi15", "版本      " + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x21:
                    //tx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5tx tlink chip upgrade index"));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);

                    int upgrademode = (mReceiveData5[currentindex + 7] & 0xFF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "升级" + tempstringoff5));
                    SpiLog.print("spi15", "升级      " + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x22:
                    //tx tlink chip enter/exit dut mode
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5tx tlink chip dut index"));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);

                    int dutmode = (mReceiveData5[currentindex + 7] & 0xFF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "Dut" + tempstringoff5));
                    SpiLog.print("spi15", "Dut       " + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x30:
                    //sub rx tlink chip setting device number
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5sub rx tlink chip setting device number index"));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData5, currentindex + 7, submacid1, 0, 6);
                    int deviceid = ((mReceiveData5[currentindex + 14] & 0xFF) * 256) + (mReceiveData5[currentindex + 13] & 0xFF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 14);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "设备号" + tempstringoff5));
                    SpiLog.print("spi15", "设备号    " + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x31:
                    //sub rx tlink chip enter/exit upgrade mode
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5sub rx tlink chip upgrade mode index"));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }
                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData5, currentindex + 7, submacid1, 0, 6);
                    int dutmode = (mReceiveData5[currentindex + 13] & 0xFF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "子升级" + tempstringoff5));
                    SpiLog.print("spi15", "子升级    " + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }

                break;
                case 0x32:
                    //sub rx tlink chip setting rode
                {
                    Log.i(TAG, "parseMessage5 = 32");
                    //runOnUiThread(() -> addText(mTextReceive5, "5sub rx tlink chip setting rode  index"));
                    int msglenSize = (mReceiveData5[currentindex + 4] & 0xFF) * 256 + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage5 sum=" + (sum & 0xFF) + "=" + (currentindex + msglenSize + 4) + "=" + (mReceiveData5[currentindex + msglenSize + 4] & 0xFF));
                        return;
                    }
                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);

                    byte[] submacid1 = new byte[6];
                    System.arraycopy(mReceiveData5, currentindex + 7, submacid1, 0, 6);
                    int mrole = (mReceiveData5[currentindex + 13] & 0xFF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 13);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "子角色" + tempstringoff5));
                    SpiLog.print("spi15", "子角色    " + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                case 0x40:
                    //tx tlink send log infos
                {
                    Log.i(TAG, "parseMessage5 = 0x40");
                    //runOnUiThread(() -> addText(mTextReceive5, formatDataTime()+"5log infos  "));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        Log.i(TAG, "parseMessage5 = 0x40 sum=" + (sum & 0xFF));
                        return;
                    }
                    Log.i(TAG, "parseMessage5 = 0x40 2");
                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);

                    int temprftotal = (mReceiveData5[currentindex + 7] & 0xFF) + ((mReceiveData5[currentindex + 8] & 0xFF) << 8) + ((mReceiveData5[currentindex + 9] & 0xFF) << 16) + ((mReceiveData5[currentindex + 10] & 0xFF) << 24);
                    int temprfloss = (mReceiveData5[currentindex + 11] & 0xFF) + ((mReceiveData5[currentindex + 12] & 0xFF) << 8) + ((mReceiveData5[currentindex + 13] & 0xFF) << 16) + ((mReceiveData5[currentindex + 14] & 0xFF) << 24);
                    if (temprftotal != 0) {
                        mTLinkRFIndex5 = temprftotal;
                        mTLinkRFlosstotal5 = temprfloss;
                    }
                    //Log.i(TAG, "="+(mReceiveData5[currentindex+7]& 0xFF) + "="+(mReceiveData5[currentindex+8]& 0xFF) + "="+(mReceiveData5[currentindex+9]& 0xFF)+ "="+(mReceiveData5[currentindex+10]& 0xFF));
                    //Log.i(TAG, "="+(mReceiveData5[currentindex+11]& 0xFF) + "="+(mReceiveData5[currentindex+12]& 0xFF) + "="+(mReceiveData5[currentindex+13]& 0xFF)+ "="+(mReceiveData5[currentindex+14]& 0xFF));
                    mTLinkspidownlosstotal5 = (mReceiveData5[currentindex + 15] & 0xFF) + ((mReceiveData5[currentindex + 16] & 0xFF) * 256);
                    mTLinkindexdownlosstotal5 = (mReceiveData5[currentindex + 17] & 0xFF) + ((mReceiveData5[currentindex + 18] & 0xFF) * 256);
                    localrfrssi5 = (mReceiveData5[currentindex + 19] & 0xFF);
                    romoterfrssi5 = (mReceiveData5[currentindex + 20] & 0xFF);
                    Log.i(TAG, "parseMessage5 mTLinkRFIndex5=" + mTLinkRFIndex5 + "=mTLinkRFlosstotal5" + mTLinkRFlosstotal5 + "=" + mTLinkspidownlosstotal5 + "=" + mTLinkindexdownlosstotal5 + "=" + localrfrssi5);
                    //revert 10 byte
                    int totalindex = 0;
                    for (int k = 0; k < 9; k++) {
                        totalindex = totalindex + (zhilingindex5[k] - 1);
                    }
                    int totalupindex = 0;
                    for (int n = 0; n < 3; n++) {
                        totalupindex = totalupindex + mTLinkindexuptotal5[n];
                    }
                    SpiLog.print("spi5", "Log:" + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    //NstartSaveLog("Log:"+ByteArraysToString(mReceiveData5,currentindex,currentindex+msglenSize+4),"spi5",mTLinkRFIndex5,mTLinkRFlosstotal5,mTLinkIndex5-1,mTLinkspidownlosstotal5,mTLinkspiuptotal5,mTLinkspiuplosstotal5,zhilingindex5-1,mTLinkindexdownlosstotal5-1,mTLinkindexuptotal5,mTLinkindexuplosstotal5,localrfrssi5,romoterfrssi5);
                    currentindex += msglenSize + 5;
                    for (int j = 0; j < deviceList.size(); j++) {
                        if (deviceList.get(j).getName().equals("spi5")) {
                            deviceList.set(j, new SpiDeviceModel("spi5", mTLinkRFIndex5, mTLinkRFlosstotal5, mTLinkIndex5 - 1, mTLinkspidownlosstotal5, mTLinkspiuptotal5, mTLinkspiuplosstotal5, totalindex, mTLinkindexdownlosstotal5, totalupindex, mTLinkindexuplosstotal5, localrfrssi5, romoterfrssi5));
                            Log.i(TAG, "parseMessage5 = 0x40 3");
                        }
                    }
                    //NstartSaveLog("spi5",mTLinkRFIndex5,mTLinkRFlosstotal5,mTLinkIndex5-1,mTLinkspidownlosstotal5,mTLinkspiuptotal5,mTLinkspiuplosstotal5,zhilingindex5-1,mTLinkindexdownlosstotal5-1,mTLinkindexuptotal5,mTLinkindexuplosstotal5,localrfrssi5,romoterfrssi5);


                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            adapter.notifyDataSetChanged();
                        }
                    });
                }
                break;
                case 0x4f:
                    //pair
                {
                    //runOnUiThread(() -> addText(mTextReceive5, "5pair index"));
                    int msglenSize = ((mReceiveData5[currentindex + 4] & 0xFF) * 256) + (mReceiveData5[currentindex + 3] & 0xFF);
                    int sum = 0;
                    for (int i = (currentindex + 2); i < (currentindex + 4 + msglenSize); i++) {
                        sum += (mReceiveData5[i] & 0xFF);
                    }
                    if ((sum & 0xFF) != (mReceiveData5[currentindex + msglenSize + 4] & 0xFF)) {
                        return;
                    }

                    int indextotalsize = (mReceiveData5[currentindex + 5] & 0xFF) + ((mReceiveData5[currentindex + 6] & 0xFF) * 256);

                    int currentstatus = (mReceiveData5[currentindex + 7] & 0xFF);
                    tempstringoff5 = ByteArraysToString(mReceiveData5, currentindex + 7, currentindex + 7);
                    runOnUiThread(() -> addText(mTextReceive5, formatDataTime() + "清log" + tempstringoff5));
                    SpiLog.print("spi15", "清log     " + ByteArraysToString(mReceiveData5, currentindex, currentindex + msglenSize + 4));
                    currentindex += msglenSize + 5;
                }
                break;
                default:
                    return;
            }
            if (mReceiveData5.length > (currentindex + 2) && mReceiveData5[currentindex] != 0) {
                threebyte = mReceiveData5[currentindex];
                fourbyte = mReceiveData5[currentindex + 1];
                cmdbyte = mReceiveData5[currentindex + 2];
                Log.i(TAG, "parseMessage5 = 55=" + mReceiveData5[currentindex]);
            } else {
                threebyte = 0;
                fourbyte = 0;
                cmdbyte = 0;
                Log.i(TAG, "parseMessage5 = 66");
            }
            Log.i(TAG, "parseMessage5 = 77");
        }
    }

    private void checkTLinkUpIndex(int mindex, int flag) {
        if (mTLinkindexuptotal[flag] > 0) {
            if (mTLinkindexuplastIndex[flag] != 0 && (mindex - mTLinkindexuplastIndex[flag]) >= 2) {
                mTLinkindexuplosstotal += mindex - mTLinkindexuplastIndex[flag] - 1;
            }
            if ((mTLinkindexuplastIndex[flag] > mindex) && ((65536 - mTLinkindexuplastIndex[flag] >= 2) || (65536 - mTLinkindexuplastIndex[flag] == 1 && mindex >= 1))) {
                mTLinkindexuplosstotal += mindex;
            }
        }
        mTLinkindexuplastIndex[flag] = mindex;
        mTLinkindexuptotal[flag]++;
    }

    private void checkTLinkUpIndex1(int mindex, int flag) {
        if (mTLinkindexuptotal1[flag] > 0) {
            if (mTLinkindexuplastIndex1[flag] != 0 && (mindex - mTLinkindexuplastIndex1[flag]) >= 2) {
                mTLinkindexuplosstotal1 += mindex - mTLinkindexuplastIndex1[flag] - 1;
            }
            if ((mTLinkindexuplastIndex1[flag] > mindex) && ((65536 - mTLinkindexuplastIndex1[flag] >= 2) || (65536 - mTLinkindexuplastIndex1[flag] == 1 && mindex >= 1))) {
                mTLinkindexuplosstotal1 += mindex;
            }
        }
        mTLinkindexuplastIndex1[flag] = mindex;
        mTLinkindexuptotal1[flag]++;
    }


    private void checkTLinkUpIndex2(int mindex, int flag) {
        if (mTLinkindexuptotal2[flag] > 0) {
            if (mTLinkindexuplastIndex2[flag] != 0 && (mindex - mTLinkindexuplastIndex2[flag]) >= 2) {
                mTLinkindexuplosstotal2 += mindex - mTLinkindexuplastIndex2[flag] - 1;
            }
            if ((mTLinkindexuplastIndex2[flag] > mindex) && ((65536 - mTLinkindexuplastIndex2[flag] >= 2) || (65536 - mTLinkindexuplastIndex2[flag] == 1 && mindex >= 1))) {
                mTLinkindexuplosstotal2 += mindex;
            }
        }
        mTLinkindexuplastIndex2[flag] = mindex;
        mTLinkindexuptotal2[flag]++;
    }

    private void checkTLinkUpIndex3(int mindex, int flag) {
        if (mTLinkindexuptotal3[flag] > 0) {
            if (mTLinkindexuplastIndex3[flag] != 0 && (mindex - mTLinkindexuplastIndex3[flag]) >= 2) {
                mTLinkindexuplosstotal3 += mindex - mTLinkindexuplastIndex3[flag] - 1;
            }
            if ((mTLinkindexuplastIndex3[flag] > mindex) && ((65536 - mTLinkindexuplastIndex3[flag] >= 2) || (65536 - mTLinkindexuplastIndex3[flag] == 1 && mindex >= 1))) {
                mTLinkindexuplosstotal3 += mindex;
            }
        }
        mTLinkindexuplastIndex3[flag] = mindex;
        mTLinkindexuptotal3[flag]++;
    }

    private void checkTLinkUpIndex4(int mindex, int flag) {
        if (mTLinkindexuptotal4[flag] > 0) {
            if (mTLinkindexuplastIndex4[flag] != 0 && (mindex - mTLinkindexuplastIndex4[flag]) >= 2) {
                mTLinkindexuplosstotal4 += mindex - mTLinkindexuplastIndex4[flag] - 1;
            }
            if ((mTLinkindexuplastIndex4[flag] > mindex) && ((65536 - mTLinkindexuplastIndex4[flag] >= 2) || (65536 - mTLinkindexuplastIndex4[flag] == 1 && mindex >= 1))) {
                mTLinkindexuplosstotal4 += mindex;
            }
        }
        mTLinkindexuplastIndex4[flag] = mindex;
        mTLinkindexuptotal4[flag]++;
    }

    private void checkTLinkUpIndex5(int mindex, int flag) {
        if (mTLinkindexuptotal5[flag] > 0) {
            if (mTLinkindexuplastIndex5[flag] != 0 && (mindex - mTLinkindexuplastIndex5[flag]) >= 2) {
                mTLinkindexuplosstotal5 += mindex - mTLinkindexuplastIndex5[flag] - 1;
            }
            if ((mTLinkindexuplastIndex5[flag] > mindex) && ((65536 - mTLinkindexuplastIndex5[flag] >= 2) || (65536 - mTLinkindexuplastIndex5[flag] == 1 && mindex >= 1))) {
                mTLinkindexuplosstotal5 += mindex;
            }
        }
        mTLinkindexuplastIndex5[flag] = mindex;
        mTLinkindexuptotal5[flag]++;
    }

    private void receiveData() throws Exception {
        int checkbeginmic = 0;
        int checkout = 0;
        int lastout = 0;
        StringBuilder hexstring = new StringBuilder();
        Log.e(TAG, "teric99 receiveData");
        if (mReceiveData == null) {
            //Log.i(TAG, "receiveData mReceiveData=null ");
            Log.e(TAG, "teric99 receiveData null");
            mReceiveData = new byte[SPI_EACH_LEN];
            return;
        }
        for (int k = 0; k < 40; k++) {
            if (mReceiveData[30 + k] != 0) {
                checkbeginmic = 1;
            }
            String hex = Integer.toHexString(mReceiveData[750 + k] & 0xff);
            hexstring.append(hex + ",");
            if (k <= 28) {
                checkout += mReceiveData[752 + k] & 0xff;
                lastout = mReceiveData[780] & 0xff;
            }
        }


        Log.i(TAG, "begin 30 hexstring = " + hexstring + "lastout=" + lastout + "checkout=" + checkout);
        //if(tempdata[0] != 0) {
        //    runOnUiThread(() -> addText(mTextSend, Arrays.toString(tempdata)));
        //}

		/*if(mTReceiverlastIndex == 0){
            mTReceiverlastIndex = lastrecindex;
		}else{
            if(lastrecindex > mTReceiverlastIndex){
				if(lastrecindex - mTReceiverlastIndex > 1){
                    mTReceiverlosstotal += lastrecindex - mTReceiverlastIndex - 1;
				}
            }else{
                if()
			}
		}*/

        parseMessage();
    }

    private void receiveData1() throws Exception {
        Log.e(TAG, "teric99 receiveData1");
        if (mReceiveData1 == null) {
            Log.e(TAG, "teric99 receiveData1 null");
            mReceiveData1 = new byte[SPI_EACH_LEN];
            return;
        }


        parseMessage1();
    }

    private void receiveData2() throws Exception {
        Log.e(TAG, "teric99 receiveData2");
        if (mReceiveData2 == null) {
            //Log.i(TAG, "receiveData mReceiveData=null ");
            Log.e(TAG, "teric99 receiveData null");
            mReceiveData2 = new byte[SPI_EACH_LEN];
            return;
        }
        parseMessage2();
    }

    private void receiveData3() throws Exception {
        if (mReceiveData3 == null) {
            //Log.i(TAG, "receiveData mReceiveData=null ");
            mReceiveData3 = new byte[SPI_EACH_LEN];
            return;
        }
        parseMessage3();
    }

    private void receiveData4() throws Exception {
        if (mReceiveData4 == null) {
            //Log.i(TAG, "receiveData mReceiveData=null ");
            mReceiveData4 = new byte[SPI_EACH_LEN];
            return;
        }
        Log.i(TAG, "  receiveData4 teric count = " + mReceiveData4.length + ", " + Arrays.toString(mReceiveData4));

        parseMessage4();
    }

    private void receiveData5() throws Exception {
        if (mReceiveData5 == null) {
            //Log.i(TAG, "receiveData mReceiveData=null ");
            mReceiveData5 = new byte[SPI_EACH_LEN];
            return;
        }
        Log.i(TAG, "  receiveData5 teric count = " + mReceiveData5.length + ", " + Arrays.toString(mReceiveData5));
        parseMessage5();
    }

    private void openReceiveFile() {
        try {
            File receiveFile = new File(PCM_RECEIVE_FILE_PATH);
            boolean result;
            if (receiveFile.exists()) {
                result = receiveFile.delete();
                if (!result) {
                    throw new Exception("");
                }
            }
            result = receiveFile.createNewFile();
            if (!result) {
                throw new Exception("");
            }
            mFOS = new FileOutputStream(PCM_RECEIVE_FILE_PATH);


            File receiveFile1 = new File(PCM_RECEIVE_FILE_PATH1);
            boolean result1;
            if (receiveFile1.exists()) {
                result1 = receiveFile1.delete();
                if (!result1) {
                    throw new Exception("");
                }
            }
            result1 = receiveFile1.createNewFile();
            if (!result1) {
                throw new Exception("");
            }
            mFOS1 = new FileOutputStream(PCM_RECEIVE_FILE_PATH1);


            File receiveFile2 = new File(PCM_RECEIVE_FILE_PATH2);
            boolean result2;
            if (receiveFile2.exists()) {
                result2 = receiveFile2.delete();
                if (!result2) {
                    throw new Exception("");
                }
            }
            result2 = receiveFile2.createNewFile();
            if (!result2) {
                throw new Exception("");
            }
            mFOS2 = new FileOutputStream(PCM_RECEIVE_FILE_PATH2);

            File receiveFile3 = new File(PCM_RECEIVE_FILE_PATH3);
            boolean result3;
            if (receiveFile3.exists()) {
                result3 = receiveFile3.delete();
                if (!result3) {
                    throw new Exception("");
                }
            }
            result3 = receiveFile3.createNewFile();
            if (!result3) {
                throw new Exception("");
            }
            mFOS3 = new FileOutputStream(PCM_RECEIVE_FILE_PATH3);

            File receiveFile4 = new File(PCM_RECEIVE_FILE_PATH4);
            boolean result4;
            if (receiveFile4.exists()) {
                result4 = receiveFile4.delete();
                if (!result4) {
                    throw new Exception("");
                }
            }
            result4 = receiveFile4.createNewFile();
            if (!result4) {
                throw new Exception("");
            }
            mFOS4 = new FileOutputStream(PCM_RECEIVE_FILE_PATH4);

            File receiveFile5 = new File(PCM_RECEIVE_FILE_PATH5);
            boolean result5;
            if (receiveFile5.exists()) {
                result5 = receiveFile5.delete();
                if (!result5) {
                    throw new Exception("");
                }
            }
            result5 = receiveFile5.createNewFile();
            if (!result5) {
                throw new Exception("");
            }
            mFOS5 = new FileOutputStream(PCM_RECEIVE_FILE_PATH5);
        } catch (Exception e) {
            Toast.makeText(getApplicationContext(), "receive pcm file is not exist", Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }
    }

    private void closeReceiveFile() {
        try {
            if (mFOS != null) {
                mFOS.close();
                mFOS = null;
            }
            if (mFOS1 != null) {
                mFOS1.close();
                mFOS1 = null;
            }
            if (mFOS2 != null) {
                mFOS2.close();
                mFOS2 = null;
            }
            if (mFOS3 != null) {
                mFOS3.close();
                mFOS3 = null;
            }
            if (mFOS4 != null) {
                mFOS4.close();
                mFOS4 = null;
            }
            if (mFOS5 != null) {
                mFOS5.close();
                mFOS5 = null;
            }
        } catch (Exception e) {
            Toast.makeText(getApplicationContext(), "close receive pcm file is failed", Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }
    }

    private int getSendInterval() {
        String text = mEditSend.getText().toString();
        try {
            return Integer.parseInt(text);
        } catch (Exception e) {
            Toast.makeText(getApplicationContext(), "text format is wrong, set default is 5", Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }

        return 10;
    }

    private int getSubHeartInterval() {
        String text = mEditSubheart.getText().toString();
        try {
            return Integer.parseInt(text);
        } catch (Exception e) {
            Toast.makeText(getApplicationContext(), "text format is wrong, set default is 0", Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }

        return 0;
    }

    private void addText(TextView textView, String content) {
        textView.append(content);
        textView.append("\n");
        /*int offset = textView.getLineCount() * textView.getLineHeight();
        if (offset > textView.getHeight()) {
            textView.scrollTo(0, offset - textView.getHeight());
        }*/
        int scrollAmount = textView.getLayout().getLineTop(textView.getLineCount()) - textView.getHeight();
        if (scrollAmount > 0) {
            textView.scrollTo(0, scrollAmount);
        }
    }

    private static final int SAMPLE_RATE = 16000;

    private void initStreamAudioTrack() {
        int bufferSize = AudioTrack.getMinBufferSize(SAMPLE_RATE, AudioFormat.CHANNEL_OUT_MONO, AudioFormat.ENCODING_PCM_16BIT);

        mStreamAudioTrack = new AudioTrack.Builder()
                .setAudioAttributes(new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build())
                .setAudioFormat(new AudioFormat.Builder()
                        .setSampleRate(SAMPLE_RATE)
                        .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                        .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                        .build())
                .setTransferMode(AudioTrack.MODE_STREAM)
                .setBufferSizeInBytes(bufferSize * 20 / 8)
                .build();

        if (mStreamAudioTrack.getState() != AudioTrack.STATE_UNINITIALIZED && mStreamAudioTrack.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
            mStreamAudioTrack.play();
        }

        mStreamAudioTrack1 = new AudioTrack.Builder()
                .setAudioAttributes(new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build())
                .setAudioFormat(new AudioFormat.Builder()
                        .setSampleRate(SAMPLE_RATE)
                        .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                        .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                        .build())
                .setTransferMode(AudioTrack.MODE_STREAM)
                .setBufferSizeInBytes(bufferSize * 20 / 8)
                .build();

        if (mStreamAudioTrack1.getState() != AudioTrack.STATE_UNINITIALIZED && mStreamAudioTrack1.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
            mStreamAudioTrack1.play();
        }

        mStreamAudioTrack2 = new AudioTrack.Builder()
                .setAudioAttributes(new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build())
                .setAudioFormat(new AudioFormat.Builder()
                        .setSampleRate(SAMPLE_RATE)
                        .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                        .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                        .build())
                .setTransferMode(AudioTrack.MODE_STREAM)
                .setBufferSizeInBytes(bufferSize * 20 / 8)
                .build();

        if (mStreamAudioTrack2.getState() != AudioTrack.STATE_UNINITIALIZED && mStreamAudioTrack2.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
            mStreamAudioTrack2.play();
        }

        mStreamAudioTrack3 = new AudioTrack.Builder()
                .setAudioAttributes(new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build())
                .setAudioFormat(new AudioFormat.Builder()
                        .setSampleRate(SAMPLE_RATE)
                        .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                        .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                        .build())
                .setTransferMode(AudioTrack.MODE_STREAM)
                .setBufferSizeInBytes(bufferSize * 20 / 8)
                .build();

        if (mStreamAudioTrack3.getState() != AudioTrack.STATE_UNINITIALIZED && mStreamAudioTrack3.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
            mStreamAudioTrack3.play();
        }

        mStreamAudioTrack4 = new AudioTrack.Builder()
                .setAudioAttributes(new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build())
                .setAudioFormat(new AudioFormat.Builder()
                        .setSampleRate(SAMPLE_RATE)
                        .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                        .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                        .build())
                .setTransferMode(AudioTrack.MODE_STREAM)
                .setBufferSizeInBytes(bufferSize * 20 / 8)
                .build();

        if (mStreamAudioTrack4.getState() != AudioTrack.STATE_UNINITIALIZED && mStreamAudioTrack4.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
            mStreamAudioTrack4.play();
        }

        mStreamAudioTrack5 = new AudioTrack.Builder()
                .setAudioAttributes(new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build())
                .setAudioFormat(new AudioFormat.Builder()
                        .setSampleRate(SAMPLE_RATE)
                        .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                        .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                        .build())
                .setTransferMode(AudioTrack.MODE_STREAM)
                .setBufferSizeInBytes(bufferSize * 20 / 8)
                .build();

        if (mStreamAudioTrack5.getState() != AudioTrack.STATE_UNINITIALIZED && mStreamAudioTrack5.getPlayState() != AudioTrack.PLAYSTATE_PLAYING) {
            mStreamAudioTrack5.play();
        }
    }

    @SuppressLint("MissingPermission")
    private void initAudioRecorder() {
        if (mAudioRecord == null) {
            mAudioRecord = new AudioRecord(
                    MediaRecorder.AudioSource.MIC, SAMPLE_RATE,
                    AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT, mAudioData.length);
        }
    }

    private byte[] trimTrailingZeros(byte[] bytes) {
        if (bytes == null) {
            return null;
        }

        int i = bytes.length - 1;
        while (i >= 0 && bytes[i] == 0) {
            i--;
        }

        return i < 0 ? new byte[0] : Arrays.copyOfRange(bytes, 0, i + 1);
    }

    private String formatDataTime() {
        return new SimpleDateFormat("HH:mm:ss", Locale.getDefault())
                .format(new Date(System.currentTimeMillis()));
    }

    private int calUnsignedChecksum(byte[] data) {
        int sum = 0;
        for (int i = 5; i <= 748; i++) {
            sum = (sum + data[i] & 0xFF) & 0xFF;
        }

        return sum;
    }

    private String ByteArraysToString(byte[] data, int begins, int ends) {
        StringBuilder hexstring = new StringBuilder();
        //Log.e(TAG,"teric0123 begins="+begins+"ends="+ends);
        for (int i = begins; i <= ends; i++) {
            hexstring.append(String.format("%02x", (data[i] & 0xff)) + ",");
        }
        //Log.e(TAG,"teric0123 hexstring="+hexstring);
        return hexstring.toString();
    }

    public void NstartSaveLog(String indstr, String mName, int rfup_total, int rfup_loss, int spidownsound_total, int spidownsound_loss, int spiupsound_total, int spiupsound_loss, int spidownindex_total, int spidownindex_loss, int spiupindex_total, int spiupindex_loss, int rflocal_rssi, int rfremote_rssi) {
        String totalstring = indstr;
        totalstring = totalstring + "rf上行: " + rfup_total + " 丢包: " + rfup_loss + " 丢包率：" + String.format("%.4f", rfup_loss / rfup_total);
        totalstring = totalstring + "下行: " + spidownsound_total + " 丢包: " + (spidownsound_loss / 3) + " 丢包率：" + String.format("%.4f", spidownsound_loss / spidownsound_total);
        totalstring = totalstring + "上行: " + spiupsound_total + " 丢包: " + spiupsound_loss + " 丢包率：" + String.format("%.4f", spiupsound_loss / spiupsound_total);
        totalstring = totalstring + "指令下行: " + spidownindex_total + " 丢包: " + spidownindex_loss + " 丢包率：" + String.format("%.4f", spidownindex_loss / spidownindex_total);
        totalstring = totalstring + "指令上行: " + spiupindex_total + " 丢包: " + spiupindex_loss + " 丢包率：" + String.format("%.4f", spiupindex_loss / spiupindex_total);
        totalstring = totalstring + "本地rssi: " + rflocal_rssi + "远端rssi: " + rfremote_rssi;
        SpiLog.print(mName, totalstring);
    }
}

package com.neviewtech.subg;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;

public class ByteArrayStorage {
    private final ArrayList<byte[]> storage = new ArrayList<>();
    
    public int addOrGetIndex(byte[] newArray) {
        if (newArray.length != 9) {
            throw new IllegalArgumentException("Array must be exactly 8 bytes");
        }
        
        for (int i = 0; i < storage.size(); i++) {
            byte[] existing = storage.get(i);
            if (compareFirst6Bytes(existing, newArray)) {
                return i;
            }
        }
        storage.add(Arrays.copyOf(newArray, 9));
        return storage.size() - 1; 
    }
    
    private boolean compareFirst6Bytes(byte[] a, byte[] b) {
        for (int i = 0; i < 6; i++) {
            if (a[i] != b[i]) {
                return false;
            }
        }
        return true; 
    }
    
    public ArrayList<byte[]> getStorage() {
        return storage;
    }
    

    public int size() {
        return storage.size();
    }
    

    public void clear() {
        storage.clear();
    }
    
    /*public static void main(String[] args) {
        ByteArrayStorage storage = new ByteArrayStorage();
        byte[] array1 = {1, 2, 3, 4, 5, 6, 7, 8};  //
        byte[] array2 = {10, 20, 30, 40, 50, 60, 70, 80};  //
        byte[] array3 = {1, 2, 3, 4, 5, 6, 9, 10};  //
        System.out.println("Add array1: " + storage.addOrGetIndex(array1));  //
        System.out.println("Add array2: " + storage.addOrGetIndex(array2));  //
        System.out.println("Add array3: " + storage.addOrGetIndex(array3));  //
        System.out.println("\nStored arrays:");
        for (int i = 0; i < storage.size(); i++) {
            byte[] arr = storage.get(i);
            System.out.printf("Index %d: %s%n", i, Arrays.toString(arr));
        }
    }*/
}
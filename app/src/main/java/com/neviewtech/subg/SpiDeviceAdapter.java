package com.neviewtech.subg;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class SpiDeviceAdapter extends RecyclerView.Adapter<SpiDeviceAdapter.ViewHolder> {
    private static int spitotal0=0;
    private static int  spitotal1=0;
    private static int  spitotal2 = 0;
    private static int  spitotal3 = 0;
    private static int  spitotal4 = 0;
    private static int  spitotal5 = 0;
    public interface OnItemClickListener {
        void onConnectClicked(SpiDeviceModel device,boolean isconnect);
        void onPairClicked(SpiDeviceModel device,boolean isPair);
    }

    private List<SpiDeviceModel> deviceList;
    private OnItemClickListener listener;

    public SpiDeviceAdapter(List<SpiDeviceModel> deviceList, OnItemClickListener listener) {
        this.deviceList = deviceList;
        this.listener = listener;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView tvupsoundtotal;
        public TextView tvdownspitotal;
        public TextView tvupspitotal;
        public TextView tvdownindextotal;
        public TextView tvupindextotal;
        public TextView tvrf_rssi;
        public ViewHolder(View view) {
            super(view);
            tvupsoundtotal = view.findViewById(R.id.upsoundtotal);
            tvdownspitotal = view.findViewById(R.id.downspitotal);
            tvupspitotal = view.findViewById(R.id.upspitotal);
            tvdownindextotal = view.findViewById(R.id.downindextotal);
            tvupindextotal = view.findViewById(R.id.upindextotal);
            tvrf_rssi = view.findViewById(R.id.rf_rssi);
        }
    }

    @Override
    public SpiDeviceAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_spi_device, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(SpiDeviceAdapter.ViewHolder holder, int position) {
        final SpiDeviceModel device = deviceList.get(position);
        String tempstring = "";
        String totalstring = "";
        double resultcheck = (double) device.getrfup_loss() / device.getrfup_total();
        tempstring = "rf上行: "+ device.getrfup_total() + " 丢包: " + device.getrfup_loss() + " 丢包率：" + String.format("%.4f", resultcheck);
        totalstring = totalstring + tempstring;
        holder.tvupsoundtotal.setText(tempstring);
        resultcheck = (double) device.getspidownsound_loss() / device.getspidownsound_total();
        tempstring = device.getName()+"下行: "+ device.getspidownsound_total() + " 丢包: " + (device.getspidownsound_loss()/3) + " 丢包率：" + String.format("%.4f", resultcheck);
        totalstring = totalstring + tempstring;
        holder.tvdownspitotal.setText(tempstring);
        resultcheck = (double) device.getspiupsound_loss() / device.getspiupsound_total();
        tempstring = device.getName()+"上行: "+ device.getspiupsound_total() + " 丢包: " + device.getspiupsound_loss() + " 丢包率：" + String.format("%.4f", resultcheck);
        holder.tvupspitotal.setText(tempstring);
        totalstring = totalstring + tempstring;
        resultcheck = (double) device.getspidownindex_loss() / device.getspidownindex_total();
        tempstring = "指令下行: "+ device.getspidownindex_total() + " 丢包: " + device.getspidownindex_loss() + " 丢包率：" + String.format("%.4f", resultcheck);
        holder.tvdownindextotal.setText(tempstring);
        totalstring = totalstring + tempstring;
        resultcheck = (double) device.getspiupindex_loss() / device.setspiupindex_total();
        tempstring ="指令上行: "+ device.setspiupindex_total() + " 丢包: " + device.getspiupindex_loss() + " 丢包率：" + String.format("%.4f", resultcheck);
        holder.tvupindextotal.setText(tempstring);
        totalstring = totalstring + tempstring;
        tempstring ="本地rssi: " + device.getrflocal_rssi() + "远端rssi: " + device.getrfremote_rssi();
        holder.tvrf_rssi.setText(tempstring);
        totalstring = totalstring + tempstring;
        if("spi0".equals(device.getName())) {
            if(spitotal0 != (device.getspidownsound_total() + device.getspiupsound_total())) {
                SpiLog.print(device.getName()+"_0", totalstring);
                spitotal0 = device.getspidownsound_total() + device.getspiupsound_total();
            }
        }else if("spi1".equals(device.getName())) {
            if(spitotal1 != (device.getspidownsound_total() + device.getspiupsound_total())) {
                SpiLog.print(device.getName()+"_0", totalstring);
                spitotal1 = device.getspidownsound_total() + device.getspiupsound_total();
            }
        }else if("spi2".equals(device.getName())) {
            if(spitotal2 != (device.getspidownsound_total() + device.getspiupsound_total())) {
                SpiLog.print(device.getName()+"_0", totalstring);
                spitotal2 = device.getspidownsound_total() + device.getspiupsound_total();
            }
        }else if("spi3".equals(device.getName())) {
            if(spitotal3 != (device.getspidownsound_total() + device.getspiupsound_total())) {
                SpiLog.print(device.getName()+"_0", totalstring);
                spitotal3 = device.getspidownsound_total() + device.getspiupsound_total();
            }
        }else if("spi5".equals(device.getName())) {
            if(spitotal5 != (device.getspidownsound_total() + device.getspiupsound_total())) {
                SpiLog.print(device.getName()+"_0", totalstring);
                spitotal5 = device.getspidownsound_total() + device.getspiupsound_total();
            }
        }else if("spi4".equals(device.getName())) {
            if(spitotal4 != (device.getspidownsound_total() + device.getspiupsound_total())) {
                SpiLog.print(device.getName()+"_0", totalstring);
                spitotal4 = device.getspidownsound_total() + device.getspiupsound_total();
            }
        }
    }

    @Override
    public int getItemCount() {
        return deviceList.size();
    }
}

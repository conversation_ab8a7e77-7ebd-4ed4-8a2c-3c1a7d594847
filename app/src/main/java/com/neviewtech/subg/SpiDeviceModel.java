package com.neviewtech.subg;

public class SpiDeviceModel {
    private String mName;
    private int rfup_total;
    private int rfup_loss;
    private int spidownsound_total;
    private int spidownsound_loss;
    private int spiupsound_total;
    private int spiupsound_loss;
    private int spidownindex_total;
    private int spidownindex_loss;
    private int spiupindex_total;
    private int spiupindex_loss;
    private int rflocal_rssi;
    private int rfremote_rssi;
    public SpiDeviceModel(String mName,int rfup_total, int rfup_loss,int spidownsound_total,int spidownsound_loss,int spiupsound_total,int spiupsound_loss,int spidownindex_total,int spidownindex_loss,int spiupindex_total,int spiupindex_loss,int rflocal_rssi,int rfremote_rssi) {
        this.mName = mName;
        this.rfup_total = rfup_total;
        this.rfup_loss = rfup_loss;
        this.spidownsound_total = spidownsound_total;
        this.spidownsound_loss = spidownsound_loss;
        this.spiupsound_total = spiupsound_total;
        this.spiupsound_loss = spiupsound_loss;
        this.spidownindex_total = spidownindex_total;
        this.spidownindex_loss = spidownindex_loss;
        this.spiupindex_total = spiupindex_total;
        this.spiupindex_loss = spiupindex_loss;
        this.rflocal_rssi = rflocal_rssi;
        this.rfremote_rssi = rfremote_rssi;
    }
    public String getName() {
        return mName;
    }
    public int getrfup_total() {
        return rfup_total;
    }
    public int getrfup_loss() {
        return rfup_loss;
    }
    public int getspidownsound_total() {
        return spidownsound_total;
    }
    public int getspidownsound_loss() {
        return spidownsound_loss;
    }
    public int getspiupsound_total() {
        return spiupsound_total;
    }
    public int getspiupsound_loss() {
        return spiupsound_loss;
    }
    public int getspidownindex_total() {
        return spidownindex_total;
    }
    public int getspidownindex_loss() {
        return spidownindex_loss;
    }
    public int setspiupindex_total() {
        return spiupindex_total;
    }
    public int getspiupindex_loss() {
        return spiupindex_loss;
    }
    public int getrflocal_rssi() {
        return rflocal_rssi;
    }
    public int getrfremote_rssi() {
        return rfremote_rssi;
    }

    public void getrfup_total(int rssi) {
        rfup_total = rssi;
    }
    public void getrfup_loss(int rssi) {
         rfup_loss = rssi;
    }
    public void getspidownsound_total(int rssi) {
         spidownsound_total = rssi;
    }
    public void getspidownsound_loss(int rssi) {
         spidownsound_loss = rssi;
    }
    public void getspiupsound_total(int rssi) {
         spiupsound_total = rssi;
    }
    public void getspiupsound_loss(int rssi) {
         spiupsound_loss = rssi;
    }
    public void getspidownindex_total(int rssi) {
         spidownindex_total = rssi;
    }
    public void getspidownindex_loss(int rssi) {
         spidownindex_loss = rssi;
    }
    public void setspiupindex_total(int rssi) {
         spiupindex_total = rssi;
    }
    public void getspiupindex_loss(int rssi) {
         spiupindex_loss = rssi;
    }
    public void getrflocal_rssi(int rssi) {
         rflocal_rssi = rssi;
    }
    public void getrfremote_rssi(int rssi) {

        rfremote_rssi = rssi = rssi;
    }
}

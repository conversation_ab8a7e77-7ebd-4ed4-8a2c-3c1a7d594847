package com.neviewtech.subg;
 
import android.os.Environment;
import android.util.Log;
 
import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
 
public class SpiLog {
  private static final String TAG = SpiLog.class.getSimpleName();
  private static final String LOG_FILE_PATH = Environment.getExternalStorageDirectory().getAbsolutePath() + "/SpiLog/";
  private static PrintWriter mPWspi0;
  private static PrintWriter mPWspi1;
	private static PrintWriter mPWspi2;
	private static PrintWriter mPWspi3;
	private static PrintWriter mPWspi4;
	private static PrintWriter mPWspi5;
	
  private static PrintWriter mPWspi10;
  private static PrintWriter mPWspi11;
	private static PrintWriter mPWspi12;
	private static PrintWriter mPWspi13;
	private static PrintWriter mPWspi14;
	private static PrintWriter mPWspi15;

  private static PrintWriter mPWspi00;
  private static PrintWriter mPWspi01;
	private static PrintWriter mPWspi02;
	private static PrintWriter mPWspi03;
	private static PrintWriter mPWspi04;
	private static PrintWriter mPWspi05;
		
    public static void print(String tempspi,String message) {
        Log.i(TAG, message);
 
        try {
			if("spi0".equals(tempspi)){
	            if(mPWspi0 == null) {
	                mPWspi0 = createPW(tempspi);
	            }
	 
	            if(mPWspi0 != null) {
	                mPWspi0.println(genCurrentTime() + " | " + message);
	                mPWspi0.flush();
	            }
			}

			if("spi1".equals(tempspi)){
	            if(mPWspi1 == null) {
	                mPWspi1 = createPW(tempspi);
	            }
	 
	            if(mPWspi1 != null) {
	                mPWspi1.println(genCurrentTime() + " | " + message);
	                mPWspi1.flush();
	            }
			}
			if("spi2".equals(tempspi)){
	            if(mPWspi2 == null) {
	                mPWspi2 = createPW(tempspi);
	            }
	 
	            if(mPWspi2 != null) {
	                mPWspi2.println(genCurrentTime() + " | " + message);
	                mPWspi2.flush();
	            }
			}
			if("spi3".equals(tempspi)){
	            if(mPWspi3 == null) {
	                mPWspi3 = createPW(tempspi);
	            }
	 
	            if(mPWspi3 != null) {
	                mPWspi3.println(genCurrentTime() + " | " + message);
	                mPWspi3.flush();
	            }
			}
			if("spi4".equals(tempspi)){
	            if(mPWspi4 == null) {
	                mPWspi4 = createPW(tempspi);
	            }
	 
	            if(mPWspi4 != null) {
	                mPWspi4.println(genCurrentTime() + " | " + message);
	                mPWspi4.flush();
	            }
			}

			if("spi5".equals(tempspi)){
	            if(mPWspi5 == null) {
	                mPWspi5 = createPW(tempspi);
	            }
	 
	            if(mPWspi5 != null) {
	                mPWspi5.println(genCurrentTime() + " | " + message);
	                mPWspi5.flush();
	            }
			}
			
      if("spi0_0".equals(tempspi)){
	            if(mPWspi00 == null) {
	                mPWspi00 = createPW(tempspi);
	            }
	 
	            if(mPWspi00 != null) {
	                mPWspi00.println(genCurrentTime() + " | " + message);
	                mPWspi00.flush();
	            }
			}

			if("spi1_0".equals(tempspi)){
	            if(mPWspi01 == null) {
	                mPWspi01 = createPW(tempspi);
	            }
	 
	            if(mPWspi01 != null) {
	                mPWspi01.println(genCurrentTime() + " | " + message);
	                mPWspi01.flush();
	            }
			}
			if("spi2_0".equals(tempspi)){
	            if(mPWspi02 == null) {
	                mPWspi02 = createPW(tempspi);
	            }
	 
	            if(mPWspi02 != null) {
	                mPWspi02.println(genCurrentTime() + " | " + message);
	                mPWspi02.flush();
	            }
			}
			if("spi3_0".equals(tempspi)){
	            if(mPWspi03 == null) {
	                mPWspi03 = createPW(tempspi);
	            }
	 
	            if(mPWspi03 != null) {
	                mPWspi03.println(genCurrentTime() + " | " + message);
	                mPWspi03.flush();
	            }
			}
			if("spi4_0".equals(tempspi)){
	            if(mPWspi04 == null) {
	                mPWspi04 = createPW(tempspi);
	            }
	 
	            if(mPWspi04 != null) {
	                mPWspi04.println(genCurrentTime() + " | " + message);
	                mPWspi04.flush();
	            }
			}

			if("spi5_0".equals(tempspi)){
	            if(mPWspi05 == null) {
	                mPWspi05 = createPW(tempspi);
	            }
	 
	            if(mPWspi05 != null) {
	                mPWspi05.println(genCurrentTime() + " | " + message);
	                mPWspi05.flush();
	            }
			}
			
     if("spi10".equals(tempspi)){
	            if(mPWspi10 == null) {
	                mPWspi10 = createPW(tempspi);
	            }
	 
	            if(mPWspi10 != null) {
	                mPWspi10.println(genCurrentTime() + " | " + message);
	                mPWspi10.flush();
	            }
			}

			if("spi11".equals(tempspi)){
	            if(mPWspi11 == null) {
	                mPWspi11 = createPW(tempspi);
	            }
	 
	            if(mPWspi11 != null) {
	                mPWspi11.println(genCurrentTime() + " | " + message);
	                mPWspi11.flush();
	            }
			}
			if("spi12".equals(tempspi)){
	            if(mPWspi12 == null) {
	                mPWspi12 = createPW(tempspi);
	            }
	 
	            if(mPWspi12 != null) {
	                mPWspi12.println(genCurrentTime() + " | " + message);
	                mPWspi12.flush();
	            }
			}
			if("spi13".equals(tempspi)){
	            if(mPWspi13 == null) {
	                mPWspi13 = createPW(tempspi);
	            }
	 
	            if(mPWspi13 != null) {
	                mPWspi13.println(genCurrentTime() + " | " + message);
	                mPWspi13.flush();
	            }
			}
			if("spi14".equals(tempspi)){
	            if(mPWspi14 == null) {
	                mPWspi14 = createPW(tempspi);
	            }
	 
	            if(mPWspi14 != null) {
	                mPWspi14.println(genCurrentTime() + " | " + message);
	                mPWspi14.flush();
	            }
			}

			if("spi15".equals(tempspi)){
	            if(mPWspi15 == null) {
	                mPWspi15 = createPW(tempspi);
	            }
	 
	            if(mPWspi15 != null) {
	                mPWspi15.println(genCurrentTime() + " | " + message);
	                mPWspi15.flush();
	            }
			}
        } catch (Exception e) {
            e.printStackTrace();
        }
 
    }
 
    public static void deleteFilesInDirectory(String directoryPath) {
        File directory = new File(directoryPath);
        if (directory.exists() && directory.isDirectory() && mPWspi0 == null && mPWspi1==null) {
            File[] files = directory.listFiles();
            
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteFilesInDirectory(file.getAbsolutePath());
                    } 
                    else {
                        file.delete();
                    }
                }
            }
        }
    }
    
    private static PrintWriter createPW(String tempspi) throws IOException{
        boolean result;
        File dir = new File(LOG_FILE_PATH);
        if(!dir.exists()) {
            result = dir.mkdirs();
        } else {
            result = true;
        }
 
        if(result) {
            File file = new File(LOG_FILE_PATH + tempspi+"_"+genFileName());
            if(!file.exists()) result = file.createNewFile();
 
            if(result) {
                return new PrintWriter(file);
            } else {
                return null;
            }
        } else {
            return null;
        }
    }
 
    private static String genFileName() {
        return new SimpleDateFormat("yyyy_MMdd_HHmmss", Locale.getDefault())
                .format(new Date(System.currentTimeMillis())) + ".log";
    }
 
    private static String genCurrentTime() {
        return new SimpleDateFormat("MM-dd HH:mm:ss.SSS", Locale.getDefault())
                .format(new Date(System.currentTimeMillis())) + ".log";
    }
}
package com.neviewtech.subg;

import android.util.Log;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class SubG {
    public static final String TAG = SubG.class.getSimpleName();
    public static final String SPIAUD_DEV_PATH = "/dev/spiaud";
    public static final String SPIAUD_TLINK_DEV_PATH = "/dev/spiaud0.0";

    static {
        System.loadLibrary("SubG");
    }

    private int mFd = 0;

    public SubG(String path) throws IOException {
        mFd = open(path);
        Log.e(TAG, "teric open spi" + path);
        if (mFd == 0) {
            Log.e(TAG, "native open returns null");
            throw new IOException();
        }
    }

    public void tryClose() {
        try {
            close(mFd);
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
    }

    public byte[] trySpiTransfer(byte[] sendByte) {
        return SpiTransfer(sendByte,mFd);
    }

    // JNI
    private native int open(String absolutePath);

    private native int close(int mfd);

    private native byte[] SpiTransfer(byte[] sendByte,int mfd);

}

#ifndef __SPIAUD_H
#define __SPIAUD_H

#if defined(__cplusplus)
extern "C" {
#endif

#define SPIAUD_DEV_PATH      "/dev/spiaud3.0"

/* Bit formats */
enum spiaud_format {
    SPIAUD_FORMAT_S16_LE = 0,
    SPIAUD_FORMAT_S32_LE,
    SPIAUD_FORMAT_S8,
    SPIAUD_FORMAT_S24_LE,

    SPIAUD_FORMAT_MAX,
};

/* Configuration for a stream */
struct spiaud_config {
    unsigned int channels;
    unsigned int rate;
    unsigned int periodSize;
    unsigned int periodCount;
    enum spiaud_format format;
};

struct spiaud {
    int fd;
    int running:1;
    int underruns;
    struct spiaud_config config;
};

unsigned int spiaud_get_min_buffer_size(struct spiaud *pAudio);
int spiaud_read(struct spiaud *pAudio, unsigned char *pData, int count);
int spiaud_write(struct spiaud *pAudio, unsigned char *pData, int count);
int spiaud_close(struct spiaud *pAudio);
struct spiaud *spiaud_open(struct spiaud_config *pConfig);
int spiaud_is_ready(struct spiaud *pAudio);

/* IOCTL commands */
#define SPI_IOC_MAGIC           'k'

/* Must set the config before getting the audio stream  */
#define SPI_IOC_WR_INIT             _IOW(SPI_IOC_MAGIC, 6, struct spiaud_config *)

#if defined(__cplusplus)
}  /* extern "C" */
#endif

#endif

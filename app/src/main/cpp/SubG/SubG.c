/*
 * Copyright 2009-2011 <PERSON><PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <termios.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <string.h>
#include <jni.h>
#include <linux/ioctl.h>
#include <malloc.h>
#include <linux/spi/spidev.h>

#include "android/log.h"

static const char *TAG = "SubG";
#define LOGI(fmt, args...) __android_log_print(ANDROID_LOG_INFO,  TAG, fmt, ##args)
#define LOGD(fmt, args...) __android_log_print(ANDROID_LOG_DEBUG, TAG, fmt, ##args)
#define LOGE(fmt, args...) __android_log_print(ANDROID_LOG_ERROR, TAG, fmt, ##args)

int subgspi_fd = -1;

/*
 * Class:     com_neviewtech_subg_SubG
 * Method:    open
 * Signature: (Ljava/lang/String;)Ljava/io/FileDescriptor;
 */
JNIEXPORT jint JNICALL Java_com_neviewtech_subg_SubG_open
        (JNIEnv *env, jobject thiz, jstring path) {

    /* Opening device */
    {
        jboolean iscopy;
        const char *path_utf = (*env)->GetStringUTFChars(env, path, &iscopy);
        LOGD("Opening spi port %s", path_utf);

        subgspi_fd = open(path_utf, O_RDWR);
        LOGD("open() subgspi_fd = %d", subgspi_fd);
        (*env)->ReleaseStringUTFChars(env, path, path_utf);
        if (subgspi_fd == -1) {
            LOGE("Cannot open spi");
            return 0;
        }
    }

    return subgspi_fd;
}

JNIEXPORT jbyteArray JNICALL Java_com_neviewtech_subg_SubG_SpiTransfer
        (JNIEnv *env, jobject thiz, jbyteArray javaArr,jint nowfd) {
	   jbyteArray arrjb = NULL;
		jbyte *jbp = NULL;
		jbyte *rjbp = NULL;
		int ret = -1;
		if (nowfd == -1) {
			return NULL; 
		}
		jsize length = (*env)->GetArrayLength(env, javaArr);
        //LOGD("SpiTransfer() length = %d", length);
		if (length <= 0) {
			return NULL; 
		}
		jbp = (jbyte *)malloc(length * sizeof(jbyte));
		rjbp = (jbyte *)malloc(length * sizeof(jbyte));
		if (!jbp || !rjbp) {
			goto cleanup; 
		}
	
		(*env)->GetByteArrayRegion(env, javaArr, 0, length, jbp);
	
		struct spi_ioc_transfer tr = {
			.tx_buf = (unsigned long)jbp,
			.rx_buf = (unsigned long)rjbp,
			.len = length,
		};
	
		ret = ioctl(nowfd, SPI_IOC_MESSAGE(1), &tr);
		if (ret < 0) {
			goto cleanup; 
		}
	
		arrjb = (*env)->NewByteArray(env, length);
		if (arrjb != NULL) {
			(*env)->SetByteArrayRegion(env, arrjb, 0, length, rjbp);
		}
	
	cleanup:
		if (jbp) free(jbp);
		if (rjbp) free(rjbp);
	
		return arrjb;

            /* int ret = -1;
            int lenght = (*env)->GetArrayLength(env, javaArr);
            jbyteArray arrjb = (*env)->NewByteArray(env, lenght);
            if(nowfd != -1){
              jbyte *jbp = (jbyte *) malloc(lenght * sizeof(jbyte));
              (*env)->GetByteArrayRegion(env,javaArr, 0, lenght, jbp);
              jbyte *rjbp = (jbyte *) malloc(lenght * sizeof(jbyte));
              struct spi_ioc_transfer tr={
                .tx_buf = (unsigned long) jbp,
                .rx_buf = (unsigned long) rjbp,
                .len =lenght,
              };
              ret = ioctl(nowfd, SPI_IOC_MESSAGE(1), &tr);
              (*env)->SetByteArrayRegion(env,arrjb, 0, lenght, rjbp);
            }
            return arrjb;*/

        }



        /*
         * Class:     com_neviewtech_subg_SubG
         * Method:    close
         * Signature: ()V
         */
JNIEXPORT jint JNICALL Java_com_neviewtech_subg_SubG_close
        (JNIEnv *env, jobject thiz,jint nowfd) {
    if(nowfd != -1) {
        close(nowfd);
    }
	subgspi_fd = -1;
	return 1;
}


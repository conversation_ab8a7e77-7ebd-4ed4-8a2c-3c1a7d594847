#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <stdarg.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <poll.h>

#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/time.h>
#include <limits.h>

#include <linux/ioctl.h>
#include "spi_audio.h"

#include "android/log.h"

#define LOG_TAG  "SPIAUD"
#define ALOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define ALOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

#define SPIAUD_SAVE_RAW_FILE     0
static FILE *gPcmRawFile = NULL;
static int gFileIndex = 0;

static struct spiaud_config sDefaultSpiAudioConfig = {
    .channels = 1,
    .rate = 16000,
    .periodSize = 320, /* (sample_rate * BUFF_DURATION_MS) / 1000 = 16000 * 20 / 1000 = 320 */
    .periodCount = 10,
    .format = SPIAUD_FORMAT_S16_LE,
};

static struct spiaud gSpiAudio = {
    .fd = -1,
};

static unsigned int spiaud_format_to_bits(enum spiaud_format format) {
    switch (format) {
    case SPIAUD_FORMAT_S32_LE:
        return 32;
    case SPIAUD_FORMAT_S24_LE:
        return 24;
    default:
    case SPIAUD_FORMAT_S16_LE:
        return 16;
    };
}

static unsigned int spiaud_frames_to_bytes(struct spiaud *pAudio, unsigned int frames) {
    return frames * pAudio->config.channels * (spiaud_format_to_bits(pAudio->config.format) >> 3);
}

unsigned int spiaud_get_min_buffer_size(struct spiaud *pAudio) {
    return spiaud_frames_to_bytes(pAudio, pAudio->config.periodSize);
}

int spiaud_read(struct spiaud *pAudio, unsigned char *pData, int count) {
    int frameSize = spiaud_frames_to_bytes(pAudio, 1);
    int maxBufferSize = spiaud_frames_to_bytes(pAudio, pAudio->config.periodSize * pAudio->config.periodCount);
    int readBytes = 0;
    int bytes = 0;
    int ret = -1;

    if (count % frameSize != 0) {
        count = (count / frameSize) * frameSize;
    }

    ALOGD("spiaud_read(),maxBufferSize=%d,frameSize=%d",maxBufferSize,frameSize);
    while (readBytes < count) {
        bytes = (count - readBytes) < maxBufferSize ? (count - readBytes) : maxBufferSize;
        ret = read(pAudio->fd, &pData[readBytes], bytes);
        if (ret <= 0) {
            ALOGD("spiaud_read() read(bytes=%d) return %d", bytes, ret);
        }
        
        if (ret < 0 ) {
            ALOGE("spiaud_read() cannot read stream data, pAudio=0x%08x, fd=%d", pAudio, pAudio->fd);
            break;
        }
        readBytes = readBytes + ret;
    }

    if ((!gPcmRawFile) && (SPIAUD_SAVE_RAW_FILE)) {
        char filePath[50] = {0};
        sprintf(filePath, "/sdcard/aispeech/spiaudio_%d.pcm", (gFileIndex % 10));
        gFileIndex++;
        gPcmRawFile = fopen(filePath, "wb");
        if (gPcmRawFile) {
            ALOGE("spiaud_read(), fopen(%s) success!", filePath);
        } else {
            ALOGE("spiaud_read(), fopen(%s) fail!!!", filePath);
        }
    }

    if (gPcmRawFile) {
        //ALOGD("spiaud_read(count=%d), fwrite(%d)", count, readBytes);
        fwrite(pData, 1, readBytes, gPcmRawFile);
    }

    ALOGD("spiaud_read() count=%d, readBytes=%d", count, readBytes);
    return readBytes > 0 ? readBytes : ret;
}
int spiaud_write(struct spiaud *pAudio, unsigned char *pData, int count) {
    int frameSize = spiaud_frames_to_bytes(pAudio, 1);
    int maxBufferSize = spiaud_frames_to_bytes(pAudio, pAudio->config.periodSize * pAudio->config.periodCount);
    int writeBytes = 0;
    int bytes = 0;
    int ret = -1;

    if (count % frameSize != 0) {
        count = (count / frameSize) * frameSize;
    }

    ALOGD("spiaud_write(),maxBufferSize=%d,frameSize=%d",maxBufferSize,frameSize);
    while (writeBytes < count) {
        bytes = (count - writeBytes) < maxBufferSize ? (count - writeBytes) : maxBufferSize;
        ret = write(pAudio->fd, &pData[writeBytes], bytes);
        if (ret <= 0) {
            ALOGD("spiaud_write() write(bytes=%d) return %d", bytes, ret);
        }
        
        if (ret < 0 ) {
            ALOGE("spiaud_write() cannot write stream data, pAudio=0x%08x, fd=%d", pAudio, pAudio->fd);
            break;
        }
        writeBytes = writeBytes + ret;
    }
    ALOGD("spiaud_write() count=%d, writeBytes=%d", count, writeBytes);
    return writeBytes > 0 ? writeBytes : ret;
}
int spiaud_close(struct spiaud *pAudio) {
    ALOGD("spiaud_close(), pAudio=0x%08x, fd=%d", pAudio, pAudio->fd);
    if (pAudio->fd >= 0) {
        close(pAudio->fd);
    } else {
        ALOGE("spiaud_close() meets error, fd(%d) is invalid, pAudio=0x%08x", pAudio->fd, pAudio);
    }

    pAudio->fd = -1;

    if (gPcmRawFile) {
        ALOGE("spiaud_close(), fclose(/sdcard/aispeech/spiaudio_%d.pcm) !", (gFileIndex-1) % 10);
        fclose(gPcmRawFile);
        gPcmRawFile = NULL;
    }

    return 0;
}

struct spiaud *spiaud_open(struct spiaud_config *pConfig) {
    struct spiaud *pAudio;

    ALOGD("spiaud_open()");
    if (gSpiAudio.fd >= 0) {
        ALOGE("spiaud_open() device '%s' has been open", SPIAUD_DEV_PATH);
        return NULL;
    }

    pAudio = &gSpiAudio;
    pAudio->config = (pConfig != NULL) ? *pConfig : sDefaultSpiAudioConfig;
    pAudio->fd = open(SPIAUD_DEV_PATH, O_RDWR);
    if (pAudio->fd < 0) {
        ALOGE("spiaud_open() cannot open device '%s'", SPIAUD_DEV_PATH);
        return NULL;
    }

    ALOGD("spiaud_open() Exit, pAudio=0x%08x, fd=%d", pAudio, pAudio->fd);
    return pAudio;
}

int spiaud_is_ready(struct spiaud *pAudio) {
    return pAudio->fd >= 0;
}



